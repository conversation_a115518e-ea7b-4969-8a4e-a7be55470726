"""
统计报告页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from datetime import datetime
from .base_page import BasePage, UtilityFunctions


class StatisticsReportPage(BasePage):
    """统计报告页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "📊 统计报告"
        self.description = "车辆轨迹数据统计分析报告"
    
    def render(self):
        """渲染统计报告页面"""
        st.subheader(self.title)
        
        if not self.has_data():
            st.warning("⚠️ 请先在'数据导入'页面上传数据")
            return
        
        df = self.get_data()
        
        # 显示数据信息
        self.show_data_info(df)
        
        # 生成统计报告
        self._generate_statistics_report(df)
    
    def _generate_statistics_report(self, df):
        """生成统计报告"""
        st.subheader("📈 综合统计报告")
        
        # 报告选项
        report_options = self._show_report_options()
        
        # 基础统计
        if report_options['basic_stats']:
            self._show_basic_statistics(df)
        
        # 时间分析
        if report_options['time_analysis'] and 'timestamp' in df.columns:
            self._show_time_analysis(df)
        
        # 车辆分析
        if report_options['vehicle_analysis'] and 'vehicle_id' in df.columns:
            self._show_vehicle_analysis(df)
        
        # 性能指标
        if report_options['performance_metrics']:
            self._show_performance_metrics(df)
        
        # 导出报告
        self._show_export_options(df)
    
    def _show_report_options(self):
        """显示报告选项"""
        st.subheader("⚙️ 报告配置")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            basic_stats = st.checkbox("基础统计", True)
        
        with col2:
            time_analysis = st.checkbox("时间分析", True)
        
        with col3:
            vehicle_analysis = st.checkbox("车辆分析", True)
        
        with col4:
            performance_metrics = st.checkbox("性能指标", True)
        
        return {
            'basic_stats': basic_stats,
            'time_analysis': time_analysis,
            'vehicle_analysis': vehicle_analysis,
            'performance_metrics': performance_metrics
        }
    
    def _show_basic_statistics(self, df):
        """显示基础统计"""
        st.subheader("📊 基础数据统计")
        
        # 数据概览
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**数据基本信息**")
            basic_info = {
                "总记录数": f"{len(df):,}",
                "数据字段数": len(df.columns),
                "数据时间跨度": self._get_time_span(df),
                "数据完整性": f"{(1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100:.1f}%"
            }
            
            for key, value in basic_info.items():
                st.metric(key, value)
        
        with col2:
            if 'speed' in df.columns:
                st.markdown("**速度统计**")
                speed_stats = {
                    "平均速度": f"{df['speed'].mean():.1f} km/h",
                    "最高速度": f"{df['speed'].max():.1f} km/h",
                    "最低速度": f"{df['speed'].min():.1f} km/h",
                    "速度标准差": f"{df['speed'].std():.1f} km/h"
                }
                
                for key, value in speed_stats.items():
                    st.metric(key, value)
        
        # 数据质量分析
        self._show_data_quality_analysis(df)
        
        # 字段统计表
        self._show_field_statistics(df)
    
    def _show_time_analysis(self, df):
        """显示时间分析"""
        st.subheader("⏰ 时间维度分析")
        
        if 'timestamp' not in df.columns:
            st.warning("⚠️ 缺少时间戳字段")
            return
        
        # 添加时间特征
        df_time = df.copy()
        df_time['hour'] = df_time['timestamp'].dt.hour
        df_time['day_of_week'] = df_time['timestamp'].dt.day_name()
        df_time['date'] = df_time['timestamp'].dt.date
        
        # 时间分布分析
        col1, col2 = st.columns(2)
        
        with col1:
            # 按小时分布
            hourly_counts = df_time['hour'].value_counts().sort_index()
            fig_hour = px.bar(
                x=hourly_counts.index,
                y=hourly_counts.values,
                title="数据按小时分布",
                labels={'x': '小时', 'y': '记录数'}
            )
            st.plotly_chart(fig_hour, use_container_width=True)
        
        with col2:
            # 按星期分布
            day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            weekly_counts = df_time['day_of_week'].value_counts()
            weekly_counts = weekly_counts.reindex([day for day in day_order if day in weekly_counts.index])
            
            fig_week = px.bar(
                x=weekly_counts.index,
                y=weekly_counts.values,
                title="数据按星期分布",
                labels={'x': '星期', 'y': '记录数'}
            )
            st.plotly_chart(fig_week, use_container_width=True)
        
        # 时间序列趋势
        daily_counts = df_time.groupby('date').size()
        fig_trend = px.line(
            x=daily_counts.index,
            y=daily_counts.values,
            title="数据量时间趋势",
            labels={'x': '日期', 'y': '记录数'}
        )
        st.plotly_chart(fig_trend, use_container_width=True)
    
    def _show_vehicle_analysis(self, df):
        """显示车辆分析"""
        st.subheader("🚗 车辆维度分析")
        
        if 'vehicle_id' not in df.columns:
            st.warning("⚠️ 缺少车辆ID字段")
            return
        
        # 车辆统计
        vehicle_stats = df.groupby('vehicle_id').agg({
            'speed': ['count', 'mean', 'max', 'std'],
            'timestamp': ['min', 'max']
        }).round(2)
        
        vehicle_stats.columns = ['记录数', '平均速度', '最高速度', '速度标准差', '开始时间', '结束时间']
        
        # 计算行驶时长
        vehicle_stats['行驶时长(小时)'] = (
            vehicle_stats['结束时间'] - vehicle_stats['开始时间']
        ).dt.total_seconds() / 3600
        
        # 显示车辆统计表
        st.dataframe(vehicle_stats, use_container_width=True)
        
        # 车辆对比分析
        col1, col2 = st.columns(2)
        
        with col1:
            # 各车辆记录数对比
            vehicle_counts = df['vehicle_id'].value_counts()
            fig_vehicle_counts = px.bar(
                x=vehicle_counts.index,
                y=vehicle_counts.values,
                title="各车辆数据量对比",
                labels={'x': '车辆ID', 'y': '记录数'}
            )
            st.plotly_chart(fig_vehicle_counts, use_container_width=True)
        
        with col2:
            # 各车辆平均速度对比
            if 'speed' in df.columns:
                vehicle_avg_speed = df.groupby('vehicle_id')['speed'].mean()
                fig_vehicle_speed = px.bar(
                    x=vehicle_avg_speed.index,
                    y=vehicle_avg_speed.values,
                    title="各车辆平均速度对比",
                    labels={'x': '车辆ID', 'y': '平均速度 (km/h)'}
                )
                st.plotly_chart(fig_vehicle_speed, use_container_width=True)
    
    def _show_performance_metrics(self, df):
        """显示性能指标"""
        st.subheader("🎯 关键性能指标")
        
        # 计算KPI
        kpis = self._calculate_kpis(df)
        
        # 显示KPI仪表盘
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("数据覆盖率", f"{kpis['data_coverage']:.1f}%")
            st.metric("平均数据质量", f"{kpis['data_quality']:.1f}%")
        
        with col2:
            if 'avg_speed' in kpis:
                st.metric("平均行驶速度", f"{kpis['avg_speed']:.1f} km/h")
            if 'speeding_rate' in kpis:
                st.metric("超速率", f"{kpis['speeding_rate']:.1f}%")
        
        with col3:
            if 'total_distance' in kpis:
                st.metric("总行驶距离", f"{kpis['total_distance']:.1f} km")
            if 'total_time' in kpis:
                st.metric("总行驶时间", f"{kpis['total_time']:.1f} 小时")
        
        with col4:
            if 'fuel_efficiency' in kpis:
                st.metric("燃油效率指数", f"{kpis['fuel_efficiency']:.1f}")
            if 'safety_score' in kpis:
                st.metric("安全驾驶评分", f"{kpis['safety_score']:.1f}")
        
        # KPI趋势图
        if 'timestamp' in df.columns and len(df) > 100:
            self._show_kpi_trends(df)
    
    def _calculate_kpis(self, df):
        """计算关键性能指标"""
        kpis = {}
        
        # 数据覆盖率（非空值比例）
        kpis['data_coverage'] = (1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100
        
        # 数据质量（基于数据完整性和合理性）
        quality_score = 100
        
        # 检查坐标合理性
        if 'latitude' in df.columns and 'longitude' in df.columns:
            invalid_coords = len(df[
                (df['latitude'] < -90) | (df['latitude'] > 90) |
                (df['longitude'] < -180) | (df['longitude'] > 180)
            ])
            quality_score -= (invalid_coords / len(df)) * 20
        
        # 检查速度合理性
        if 'speed' in df.columns:
            invalid_speeds = len(df[(df['speed'] < 0) | (df['speed'] > 200)])
            quality_score -= (invalid_speeds / len(df)) * 20
        
        kpis['data_quality'] = max(0, quality_score)
        
        # 行驶相关指标
        if 'speed' in df.columns:
            kpis['avg_speed'] = df['speed'].mean()
            
            # 超速率
            speed_limit = st.session_state.get('speed_limit', 60)
            kpis['speeding_rate'] = len(df[df['speed'] > speed_limit]) / len(df) * 100
        
        # 距离和时间
        if 'distance' in df.columns:
            kpis['total_distance'] = df['distance'].sum() / 1000  # 转换为公里
        
        if 'timestamp' in df.columns and len(df) > 1:
            kpis['total_time'] = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
        
        # 燃油效率指数（基于速度稳定性）
        if 'speed' in df.columns:
            speed_stability = 100 - min(df['speed'].std(), 100)
            kpis['fuel_efficiency'] = speed_stability
        
        # 安全驾驶评分
        if 'speed' in df.columns:
            safety_score = 100 - kpis.get('speeding_rate', 0)
            kpis['safety_score'] = max(0, safety_score)
        
        return kpis
    
    def _show_kpi_trends(self, df):
        """显示KPI趋势"""
        st.subheader("📈 KPI趋势分析")
        
        # 按时间段聚合数据
        df_trend = df.copy()
        df_trend['date'] = df_trend['timestamp'].dt.date
        
        daily_kpis = df_trend.groupby('date').agg({
            'speed': ['mean', 'max', 'std']
        }).round(2)
        
        daily_kpis.columns = ['平均速度', '最高速度', '速度标准差']
        
        # 创建趋势图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('平均速度趋势', '最高速度趋势', '速度稳定性趋势', '数据量趋势'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 平均速度趋势
        fig.add_trace(
            go.Scatter(
                x=daily_kpis.index,
                y=daily_kpis['平均速度'],
                mode='lines+markers',
                name='平均速度'
            ),
            row=1, col=1
        )
        
        # 最高速度趋势
        fig.add_trace(
            go.Scatter(
                x=daily_kpis.index,
                y=daily_kpis['最高速度'],
                mode='lines+markers',
                name='最高速度'
            ),
            row=1, col=2
        )
        
        # 速度稳定性趋势（标准差越小越稳定）
        fig.add_trace(
            go.Scatter(
                x=daily_kpis.index,
                y=daily_kpis['速度标准差'],
                mode='lines+markers',
                name='速度标准差'
            ),
            row=2, col=1
        )
        
        # 数据量趋势
        daily_counts = df_trend.groupby('date').size()
        fig.add_trace(
            go.Scatter(
                x=daily_counts.index,
                y=daily_counts.values,
                mode='lines+markers',
                name='数据量'
            ),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
    
    def _get_time_span(self, df):
        """获取数据时间跨度"""
        if 'timestamp' in df.columns and len(df) > 1:
            time_span = df['timestamp'].max() - df['timestamp'].min()
            days = time_span.days
            hours = time_span.seconds // 3600
            return f"{days}天{hours}小时"
        return "未知"
    
    def _show_data_quality_analysis(self, df):
        """显示数据质量分析"""
        st.subheader("🔍 数据质量分析")
        
        # 缺失值分析
        missing_data = df.isnull().sum()
        missing_percentage = (missing_data / len(df)) * 100
        
        if missing_data.sum() > 0:
            quality_df = pd.DataFrame({
                '字段': missing_data.index,
                '缺失数量': missing_data.values,
                '缺失比例(%)': missing_percentage.values
            })
            quality_df = quality_df[quality_df['缺失数量'] > 0]
            
            st.dataframe(quality_df, use_container_width=True)
        else:
            st.success("✅ 数据完整，无缺失值")
        
        # 异常值检测
        if 'speed' in df.columns:
            outliers = len(df[(df['speed'] < 0) | (df['speed'] > 200)])
            if outliers > 0:
                st.warning(f"⚠️ 检测到 {outliers} 个异常速度值")
            else:
                st.success("✅ 速度数据正常")
    
    def _show_field_statistics(self, df):
        """显示字段统计"""
        st.subheader("📋 字段统计信息")
        
        field_stats = []
        for col in df.columns:
            if df[col].dtype in ['int64', 'float64']:
                stats = {
                    '字段名': col,
                    '数据类型': str(df[col].dtype),
                    '非空值数': df[col].count(),
                    '唯一值数': df[col].nunique(),
                    '最小值': df[col].min(),
                    '最大值': df[col].max(),
                    '平均值': df[col].mean(),
                    '标准差': df[col].std()
                }
            else:
                stats = {
                    '字段名': col,
                    '数据类型': str(df[col].dtype),
                    '非空值数': df[col].count(),
                    '唯一值数': df[col].nunique(),
                    '最小值': '-',
                    '最大值': '-',
                    '平均值': '-',
                    '标准差': '-'
                }
            field_stats.append(stats)
        
        stats_df = pd.DataFrame(field_stats)
        st.dataframe(stats_df, use_container_width=True)
    
    def _show_export_options(self, df):
        """显示导出选项"""
        st.subheader("📥 报告导出")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("📊 导出统计报告"):
                report_data = self._generate_report_data(df)
                csv = report_data.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载统计报告CSV",
                    data=csv,
                    file_name=f"vehicle_statistics_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        
        with col2:
            if st.button("📈 导出KPI数据"):
                kpis = self._calculate_kpis(df)
                kpi_df = pd.DataFrame([kpis])
                csv = kpi_df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载KPI数据CSV",
                    data=csv,
                    file_name=f"vehicle_kpis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
        
        with col3:
            if st.button("🗂️ 导出完整数据"):
                csv = df.to_csv(index=False, encoding='utf-8-sig')
                st.download_button(
                    label="下载完整数据CSV",
                    data=csv,
                    file_name=f"vehicle_full_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
    
    def _generate_report_data(self, df):
        """生成报告数据"""
        # 这里可以根据需要生成更详细的报告数据
        summary_stats = df.describe()
        return summary_stats
