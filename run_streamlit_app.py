#!/usr/bin/env python3
"""
Streamlit应用程序启动脚本
"""

import subprocess
import sys
import os
from pathlib import Path

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = ['streamlit', 'plotly', 'pandas', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行以下命令安装依赖:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """启动Streamlit应用程序"""
    print("🚀 启动Streamlit应用程序...")
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 获取当前脚本目录
    current_dir = Path(__file__).parent
    app_file = current_dir / "streamlit_app.py"
    
    if not app_file.exists():
        print(f"❌ 找不到应用程序文件: {app_file}")
        sys.exit(1)
    
    # 启动Streamlit
    try:
        print("📱 正在启动Streamlit服务器...")
        print("🌐 应用程序将在浏览器中自动打开")
        print("🔗 访问地址: http://localhost:8501")
        print("⏹️  按 Ctrl+C 停止服务器")
        print("-" * 50)
        
        # 启动streamlit
        subprocess.run([
            sys.executable, "-m", "streamlit", "run", 
            str(app_file),
            "--server.port=8501",
            "--server.headless=false",
            "--browser.gatherUsageStats=false"
        ])
        
    except KeyboardInterrupt:
        print("\n👋 应用程序已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
