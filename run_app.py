#!/usr/bin/env python3
"""
Panel应用程序启动脚本
"""

import panel as pn
from panel_app_template import create_app

def main():
    """启动Panel应用程序"""
    # 配置Panel
    pn.config.title = "Panel应用程序模板"
    pn.config.sizing_mode = "stretch_width"
    
    # 创建应用程序
    app = create_app()
    
    # 启动服务器
    print("启动Panel应用程序...")
    print("访问地址: http://localhost:5007")
    print("按 Ctrl+C 停止服务器")
    
    app.show(port=5007, autoreload=True, show=True)

if __name__ == "__main__":
    main()
