"""
轨迹可视化页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from .base_page import BasePage, UtilityFunctions


class TrajectoryVisualizationPage(BasePage):
    """轨迹可视化页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "🗺️ 轨迹可视化"
        self.description = "车辆轨迹地图可视化和分析"
    
    def render(self):
        """渲染轨迹可视化页面"""
        st.subheader(self.title)
        
        if not self.has_data():
            st.warning("⚠️ 请先在'数据导入'页面上传数据")
            return
        
        df = self.get_data()
        
        # 显示数据信息
        self.show_data_info(df)
        
        # 可视化选项
        self._show_visualization_options(df)
    
    def _show_visualization_options(self, df):
        """显示可视化选项"""
        st.subheader("🎨 可视化选项")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            show_trajectory = st.checkbox("显示轨迹线", True)
            show_speed_color = st.checkbox("速度颜色映射", True)
        
        with col2:
            show_markers = st.checkbox("显示轨迹点", False)
            show_speeding = st.checkbox("高亮超速区域", True)
        
        with col3:
            map_style = st.selectbox("地图样式", 
                                   ["open-street-map", "carto-positron", "carto-darkmatter", "stamen-terrain"])
        
        # 数据筛选
        self._show_data_filters(df)
        
        # 生成地图
        filtered_df = self._apply_filters(df)
        if len(filtered_df) > 0:
            self._create_trajectory_map(filtered_df, show_trajectory, show_speed_color, 
                                      show_markers, show_speeding, map_style)
        else:
            st.warning("⚠️ 筛选后没有数据")
    
    def _show_data_filters(self, df):
        """显示数据筛选选项"""
        st.subheader("🔍 数据筛选")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 时间筛选
            if 'timestamp' in df.columns:
                min_time = df['timestamp'].min()
                max_time = df['timestamp'].max()
                
                time_range = st.date_input(
                    "选择时间范围",
                    value=(min_time.date(), max_time.date()),
                    min_value=min_time.date(),
                    max_value=max_time.date()
                )
                
                st.session_state.time_filter = time_range
        
        with col2:
            # 车辆筛选
            if 'vehicle_id' in df.columns:
                vehicles = df['vehicle_id'].unique()
                selected_vehicles = st.multiselect(
                    "选择车辆",
                    vehicles,
                    default=vehicles[:5] if len(vehicles) > 5 else vehicles
                )
                st.session_state.vehicle_filter = selected_vehicles
        
        # 速度筛选
        if 'speed' in df.columns:
            speed_range = st.slider(
                "速度范围 (km/h)",
                float(df['speed'].min()),
                float(df['speed'].max()),
                (float(df['speed'].min()), float(df['speed'].max()))
            )
            st.session_state.speed_filter = speed_range
    
    def _apply_filters(self, df):
        """应用筛选条件"""
        filtered_df = df.copy()
        
        # 时间筛选
        if 'time_filter' in st.session_state and 'timestamp' in df.columns:
            time_range = st.session_state.time_filter
            if len(time_range) == 2:
                start_date, end_date = time_range
                filtered_df = filtered_df[
                    (filtered_df['timestamp'].dt.date >= start_date) &
                    (filtered_df['timestamp'].dt.date <= end_date)
                ]
        
        # 车辆筛选
        if 'vehicle_filter' in st.session_state and 'vehicle_id' in df.columns:
            selected_vehicles = st.session_state.vehicle_filter
            if selected_vehicles:
                filtered_df = filtered_df[filtered_df['vehicle_id'].isin(selected_vehicles)]
        
        # 速度筛选
        if 'speed_filter' in st.session_state and 'speed' in df.columns:
            speed_range = st.session_state.speed_filter
            filtered_df = filtered_df[
                (filtered_df['speed'] >= speed_range[0]) &
                (filtered_df['speed'] <= speed_range[1])
            ]
        
        return filtered_df
    
    def _create_trajectory_map(self, df, show_trajectory, show_speed_color, 
                             show_markers, show_speeding, map_style):
        """创建轨迹地图"""
        st.subheader("🗺️ 轨迹地图")
        
        # 检查必需的字段
        if not self.validate_required_fields(df, ['latitude', 'longitude']):
            return
        
        # 数据采样（如果数据量太大）
        plot_df = self._sample_data_for_plot(df)
        
        # 创建地图
        fig = go.Figure()
        
        # 添加轨迹线
        if show_trajectory:
            if 'vehicle_id' in plot_df.columns:
                # 按车辆分组绘制轨迹
                for vehicle_id in plot_df['vehicle_id'].unique():
                    vehicle_data = plot_df[plot_df['vehicle_id'] == vehicle_id].sort_values('timestamp')
                    
                    if show_speed_color and 'speed' in vehicle_data.columns:
                        # 使用速度颜色映射
                        fig.add_trace(go.Scattermapbox(
                            lat=vehicle_data['latitude'],
                            lon=vehicle_data['longitude'],
                            mode='lines+markers' if show_markers else 'lines',
                            line=dict(width=3),
                            marker=dict(
                                size=6,
                                color=vehicle_data['speed'],
                                colorscale='Viridis',
                                showscale=True,
                                colorbar=dict(title="速度 (km/h)")
                            ),
                            name=f'车辆 {vehicle_id}',
                            hovertemplate='<b>车辆:</b> %{text}<br>' +
                                        '<b>纬度:</b> %{lat}<br>' +
                                        '<b>经度:</b> %{lon}<br>' +
                                        '<b>速度:</b> %{marker.color:.1f} km/h<extra></extra>',
                            text=[vehicle_id] * len(vehicle_data)
                        ))
                    else:
                        # 普通轨迹线
                        fig.add_trace(go.Scattermapbox(
                            lat=vehicle_data['latitude'],
                            lon=vehicle_data['longitude'],
                            mode='lines+markers' if show_markers else 'lines',
                            line=dict(width=3),
                            marker=dict(size=6),
                            name=f'车辆 {vehicle_id}',
                            hovertemplate='<b>车辆:</b> %{text}<br>' +
                                        '<b>纬度:</b> %{lat}<br>' +
                                        '<b>经度:</b> %{lon}<extra></extra>',
                            text=[vehicle_id] * len(vehicle_data)
                        ))
            else:
                # 单一轨迹
                if show_speed_color and 'speed' in plot_df.columns:
                    fig.add_trace(go.Scattermapbox(
                        lat=plot_df['latitude'],
                        lon=plot_df['longitude'],
                        mode='lines+markers' if show_markers else 'lines',
                        line=dict(width=3),
                        marker=dict(
                            size=6,
                            color=plot_df['speed'],
                            colorscale='Viridis',
                            showscale=True,
                            colorbar=dict(title="速度 (km/h)")
                        ),
                        name='轨迹',
                        hovertemplate='<b>纬度:</b> %{lat}<br>' +
                                    '<b>经度:</b> %{lon}<br>' +
                                    '<b>速度:</b> %{marker.color:.1f} km/h<extra></extra>'
                    ))
                else:
                    fig.add_trace(go.Scattermapbox(
                        lat=plot_df['latitude'],
                        lon=plot_df['longitude'],
                        mode='lines+markers' if show_markers else 'lines',
                        line=dict(width=3),
                        marker=dict(size=6),
                        name='轨迹'
                    ))
        
        # 添加超速区域高亮
        if show_speeding and 'speed' in plot_df.columns:
            speed_limit = st.session_state.get('speed_limit', 60)
            speeding_data = plot_df[plot_df['speed'] > speed_limit]
            
            if len(speeding_data) > 0:
                fig.add_trace(go.Scattermapbox(
                    lat=speeding_data['latitude'],
                    lon=speeding_data['longitude'],
                    mode='markers',
                    marker=dict(
                        size=8,
                        color='red',
                        symbol='circle'
                    ),
                    name=f'超速点 (>{speed_limit} km/h)',
                    hovertemplate='<b>超速!</b><br>' +
                                '<b>速度:</b> %{text:.1f} km/h<br>' +
                                '<b>纬度:</b> %{lat}<br>' +
                                '<b>经度:</b> %{lon}<extra></extra>',
                    text=speeding_data['speed']
                ))
        
        # 设置地图布局
        center_lat = plot_df['latitude'].mean()
        center_lon = plot_df['longitude'].mean()
        
        fig.update_layout(
            mapbox=dict(
                style=map_style,
                center=dict(lat=center_lat, lon=center_lon),
                zoom=12
            ),
            height=600,
            margin=dict(l=0, r=0, t=0, b=0)
        )
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 显示统计信息
        self._show_map_statistics(plot_df)
    
    def _sample_data_for_plot(self, df):
        """为绘图采样数据"""
        max_points = 5000  # 最大绘图点数
        
        if len(df) > max_points:
            st.info(f"📊 数据量较大，已采样至 {max_points} 个点进行可视化")
            # 按时间均匀采样
            if 'timestamp' in df.columns:
                df_sorted = df.sort_values('timestamp')
                indices = np.linspace(0, len(df_sorted)-1, max_points, dtype=int)
                return df_sorted.iloc[indices]
            else:
                return df.sample(n=max_points)
        
        return df
    
    def _show_map_statistics(self, df):
        """显示地图统计信息"""
        st.subheader("📊 轨迹统计")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("轨迹点数", f"{len(df):,}")
        
        with col2:
            if 'distance' in df.columns:
                total_distance = df['distance'].sum() / 1000  # 转换为公里
                st.metric("总距离 (km)", f"{total_distance:.2f}")
        
        with col3:
            if 'speed' in df.columns:
                avg_speed = df['speed'].mean()
                st.metric("平均速度 (km/h)", f"{avg_speed:.1f}")
        
        with col4:
            if 'speed' in df.columns:
                speed_limit = st.session_state.get('speed_limit', 60)
                speeding_points = len(df[df['speed'] > speed_limit])
                speeding_rate = (speeding_points / len(df)) * 100
                st.metric("超速率 (%)", f"{speeding_rate:.1f}")
