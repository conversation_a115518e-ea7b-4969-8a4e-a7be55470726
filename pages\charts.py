import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import altair as alt

def render():
    """渲染图表展示页面"""
    
    st.markdown("""
    ### 📉 图表展示中心
    
    这里展示了各种类型的交互式图表，帮助您更好地理解和展示数据。
    """)
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📊 基础图表", "📈 高级图表", "🎯 实时图表", "🎨 自定义图表"])
    
    with tab1:
        st.subheader("📊 基础图表示例")
        
        # 生成示例数据
        np.random.seed(42)
        dates = pd.date_range('2024-01-01', periods=30, freq='D')
        sales_data = pd.DataFrame({
            '日期': dates,
            '销售额': np.random.normal(1000, 200, 30),
            '访问量': np.random.poisson(500, 30),
            '转化率': np.random.beta(2, 8, 30) * 100
        })
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 折线图
            st.subheader("📈 销售趋势")
            fig_line = px.line(sales_data, x='日期', y='销售额', 
                              title='30天销售趋势')
            fig_line.update_layout(height=300)
            st.plotly_chart(fig_line, use_container_width=True)
            
            # 柱状图
            st.subheader("📊 访问量分布")
            fig_bar = px.bar(sales_data.tail(7), x='日期', y='访问量',
                            title='最近7天访问量')
            fig_bar.update_layout(height=300)
            st.plotly_chart(fig_bar, use_container_width=True)
        
        with col2:
            # 散点图
            st.subheader("🎯 销售额 vs 访问量")
            fig_scatter = px.scatter(sales_data, x='访问量', y='销售额',
                                   size='转化率', title='销售额与访问量关系')
            fig_scatter.update_layout(height=300)
            st.plotly_chart(fig_scatter, use_container_width=True)
            
            # 饼图
            st.subheader("🥧 产品类型分布")
            product_data = pd.DataFrame({
                '产品': ['产品A', '产品B', '产品C', '产品D'],
                '销量': [30, 25, 20, 25]
            })
            fig_pie = px.pie(product_data, values='销量', names='产品',
                           title='产品销量分布')
            fig_pie.update_layout(height=300)
            st.plotly_chart(fig_pie, use_container_width=True)
    
    with tab2:
        st.subheader("📈 高级图表示例")
        
        # 子图表
        st.subheader("📊 多指标仪表板")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('销售趋势', '访问量热力图', '转化率分布', '地区对比'),
            specs=[[{"secondary_y": True}, {"type": "heatmap"}],
                   [{"type": "histogram"}, {"type": "bar"}]]
        )
        
        # 添加销售趋势线
        fig.add_trace(
            go.Scatter(x=sales_data['日期'], y=sales_data['销售额'], 
                      name='销售额', line=dict(color='blue')),
            row=1, col=1
        )
        
        # 添加访问量到第二个Y轴
        fig.add_trace(
            go.Scatter(x=sales_data['日期'], y=sales_data['访问量'], 
                      name='访问量', line=dict(color='red')),
            row=1, col=1, secondary_y=True
        )
        
        # 热力图数据
        heatmap_data = np.random.rand(7, 5)
        fig.add_trace(
            go.Heatmap(z=heatmap_data, colorscale='Viridis'),
            row=1, col=2
        )
        
        # 直方图
        fig.add_trace(
            go.Histogram(x=sales_data['转化率'], name='转化率分布'),
            row=2, col=1
        )
        
        # 地区对比柱状图
        regions = ['北京', '上海', '广州', '深圳', '杭州']
        values = np.random.randint(100, 1000, 5)
        fig.add_trace(
            go.Bar(x=regions, y=values, name='地区销售额'),
            row=2, col=2
        )
        
        fig.update_layout(height=600, showlegend=False)
        st.plotly_chart(fig, use_container_width=True)
        
        # 3D图表
        st.subheader("🌐 3D散点图")
        
        # 生成3D数据
        n_points = 100
        x = np.random.randn(n_points)
        y = np.random.randn(n_points)
        z = x + y + np.random.randn(n_points) * 0.5
        
        fig_3d = go.Figure(data=[go.Scatter3d(
            x=x, y=y, z=z,
            mode='markers',
            marker=dict(
                size=5,
                color=z,
                colorscale='Viridis',
                showscale=True
            )
        )])
        
        fig_3d.update_layout(
            title='3D数据分布',
            scene=dict(
                xaxis_title='X轴',
                yaxis_title='Y轴',
                zaxis_title='Z轴'
            ),
            height=500
        )
        
        st.plotly_chart(fig_3d, use_container_width=True)
    
    with tab3:
        st.subheader("🎯 实时图表")
        
        # 实时数据模拟
        if st.button("🔄 刷新实时数据"):
            st.rerun()
        
        # 实时指标
        col1, col2, col3 = st.columns(3)
        
        with col1:
            current_value = np.random.randint(80, 120)
            st.metric("实时CPU使用率", f"{current_value}%", f"{current_value-100}%")
        
        with col2:
            current_memory = np.random.randint(60, 90)
            st.metric("内存使用率", f"{current_memory}%", f"{current_memory-75}%")
        
        with col3:
            current_network = np.random.randint(10, 50)
            st.metric("网络流量", f"{current_network} MB/s", f"{current_network-30} MB/s")
        
        # 实时折线图
        st.subheader("📈 实时监控图表")
        
        # 生成实时数据
        time_points = pd.date_range('2024-01-01 00:00:00', periods=50, freq='1min')
        real_time_data = pd.DataFrame({
            '时间': time_points,
            'CPU': np.random.randint(70, 100, 50),
            '内存': np.random.randint(60, 90, 50),
            '网络': np.random.randint(10, 50, 50)
        })
        
        fig_realtime = go.Figure()
        
        fig_realtime.add_trace(go.Scatter(
            x=real_time_data['时间'],
            y=real_time_data['CPU'],
            mode='lines+markers',
            name='CPU使用率',
            line=dict(color='red')
        ))
        
        fig_realtime.add_trace(go.Scatter(
            x=real_time_data['时间'],
            y=real_time_data['内存'],
            mode='lines+markers',
            name='内存使用率',
            line=dict(color='blue')
        ))
        
        fig_realtime.add_trace(go.Scatter(
            x=real_time_data['时间'],
            y=real_time_data['网络'],
            mode='lines+markers',
            name='网络流量',
            line=dict(color='green')
        ))
        
        fig_realtime.update_layout(
            title='系统实时监控',
            xaxis_title='时间',
            yaxis_title='使用率(%)',
            height=400
        )
        
        st.plotly_chart(fig_realtime, use_container_width=True)
        
        # 仪表盘
        st.subheader("🎛️ 仪表盘")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            fig_gauge1 = go.Figure(go.Indicator(
                mode = "gauge+number+delta",
                value = current_value,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "CPU使用率"},
                delta = {'reference': 80},
                gauge = {
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "gray"}],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 90}}))
            fig_gauge1.update_layout(height=300)
            st.plotly_chart(fig_gauge1, use_container_width=True)
        
        with col2:
            fig_gauge2 = go.Figure(go.Indicator(
                mode = "gauge+number+delta",
                value = current_memory,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "内存使用率"},
                delta = {'reference': 70},
                gauge = {
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkgreen"},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "gray"}],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 85}}))
            fig_gauge2.update_layout(height=300)
            st.plotly_chart(fig_gauge2, use_container_width=True)
        
        with col3:
            fig_gauge3 = go.Figure(go.Indicator(
                mode = "gauge+number+delta",
                value = current_network,
                domain = {'x': [0, 1], 'y': [0, 1]},
                title = {'text': "网络流量"},
                delta = {'reference': 25},
                gauge = {
                    'axis': {'range': [None, 60]},
                    'bar': {'color': "darkorange"},
                    'steps': [
                        {'range': [0, 20], 'color': "lightgray"},
                        {'range': [20, 40], 'color': "gray"}],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 50}}))
            fig_gauge3.update_layout(height=300)
            st.plotly_chart(fig_gauge3, use_container_width=True)
    
    with tab4:
        st.subheader("🎨 自定义图表")
        
        st.markdown("在这里您可以创建自定义的图表配置")
        
        # 图表配置选项
        col1, col2 = st.columns([1, 2])
        
        with col1:
            st.subheader("⚙️ 图表配置")
            
            chart_type = st.selectbox(
                "图表类型",
                ["折线图", "柱状图", "散点图", "面积图", "雷达图"]
            )
            
            color_scheme = st.selectbox(
                "颜色方案",
                ["Plotly", "Viridis", "Plasma", "Inferno", "Magma"]
            )
            
            show_grid = st.checkbox("显示网格", value=True)
            show_legend = st.checkbox("显示图例", value=True)
            
            chart_height = st.slider("图表高度", 300, 800, 400)
        
        with col2:
            st.subheader("📊 预览图表")
            
            # 生成自定义数据
            custom_data = pd.DataFrame({
                'X': np.arange(1, 21),
                'Y1': np.random.randint(10, 100, 20),
                'Y2': np.random.randint(20, 80, 20),
                'Y3': np.random.randint(5, 60, 20)
            })
            
            if chart_type == "折线图":
                fig = px.line(custom_data, x='X', y=['Y1', 'Y2', 'Y3'],
                             color_discrete_sequence=px.colors.qualitative.Set1)
            elif chart_type == "柱状图":
                fig = px.bar(custom_data, x='X', y='Y1')
            elif chart_type == "散点图":
                fig = px.scatter(custom_data, x='Y1', y='Y2', size='Y3')
            elif chart_type == "面积图":
                fig = px.area(custom_data, x='X', y='Y1')
            elif chart_type == "雷达图":
                categories = ['指标A', '指标B', '指标C', '指标D', '指标E']
                values = np.random.randint(1, 10, 5)
                
                fig = go.Figure()
                fig.add_trace(go.Scatterpolar(
                    r=values,
                    theta=categories,
                    fill='toself',
                    name='数据系列'
                ))
                fig.update_layout(
                    polar=dict(
                        radialaxis=dict(
                            visible=True,
                            range=[0, 10]
                        )),
                    showlegend=show_legend
                )
            
            # 应用配置
            if chart_type != "雷达图":
                fig.update_layout(
                    showlegend=show_legend,
                    height=chart_height,
                    xaxis_showgrid=show_grid,
                    yaxis_showgrid=show_grid
                )
            
            st.plotly_chart(fig, use_container_width=True)
        
        # 图表代码生成
        st.subheader("💻 生成代码")
        
        if st.button("生成图表代码"):
            code = f"""
import plotly.express as px
import pandas as pd
import numpy as np

# 数据准备
data = pd.DataFrame({{
    'X': np.arange(1, 21),
    'Y1': np.random.randint(10, 100, 20),
    'Y2': np.random.randint(20, 80, 20),
    'Y3': np.random.randint(5, 60, 20)
}})

# 创建图表
fig = px.{chart_type.replace('图', '')}(data, x='X', y='Y1')

# 配置选项
fig.update_layout(
    showlegend={show_legend},
    height={chart_height},
    xaxis_showgrid={show_grid},
    yaxis_showgrid={show_grid}
)

# 显示图表
fig.show()
"""
            st.code(code, language='python')
