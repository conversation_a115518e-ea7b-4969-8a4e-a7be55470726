"""
驾驶行为分析页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from .base_page import BasePage, UtilityFunctions


class DrivingBehaviorPage(BasePage):
    """驾驶行为分析页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "🚗 驾驶行为分析"
        self.description = "车辆驾驶行为模式分析和评估"
    
    def render(self):
        """渲染驾驶行为分析页面"""
        st.subheader(self.title)
        
        if not self.has_data():
            st.warning("⚠️ 请先在'数据导入'页面上传数据")
            return
        
        df = self.get_data()
        
        if 'speed' not in df.columns:
            st.error("❌ 数据中缺少速度字段，无法进行驾驶行为分析")
            return
        
        # 显示数据信息
        self.show_data_info(df)
        
        # 执行驾驶行为分析
        self._perform_behavior_analysis(df)
    
    def _perform_behavior_analysis(self, df):
        """执行驾驶行为分析"""
        # 计算驾驶行为指标
        behavior_metrics = self._calculate_behavior_metrics(df)
        
        # 显示行为指标概览
        self._show_behavior_overview(behavior_metrics)
        
        # 显示详细分析
        self._show_detailed_analysis(df, behavior_metrics)
        
        # 显示行为评分
        self._show_behavior_scoring(behavior_metrics)
    
    def _calculate_behavior_metrics(self, df):
        """计算驾驶行为指标"""
        metrics = {}
        
        # 基础统计
        metrics['total_records'] = len(df)
        metrics['avg_speed'] = df['speed'].mean()
        metrics['max_speed'] = df['speed'].max()
        metrics['speed_std'] = df['speed'].std()
        
        # 超速分析
        speed_limit = st.session_state.get('speed_limit', 60)
        speeding_events = UtilityFunctions.detect_speeding_events(df, speed_limit)
        metrics['speeding_events'] = len(speeding_events)
        metrics['speeding_rate'] = len(df[df['speed'] > speed_limit]) / len(df) * 100
        
        if speeding_events:
            metrics['total_speeding_time'] = sum([event['duration'] for event in speeding_events]) / 60  # 分钟
            metrics['avg_speeding_duration'] = np.mean([event['duration'] for event in speeding_events])
        else:
            metrics['total_speeding_time'] = 0
            metrics['avg_speeding_duration'] = 0
        
        # 加速度分析
        if 'timestamp' in df.columns and len(df) > 1:
            # 计算加速度
            speed_changes = df['speed'].diff() / 3.6  # 转换为m/s
            time_diffs = df['timestamp'].diff().dt.total_seconds()
            accelerations = speed_changes / time_diffs
            
            # 过滤异常值
            accelerations = accelerations[abs(accelerations) < 10]  # 过滤掉明显异常的加速度
            
            metrics['avg_acceleration'] = accelerations.mean()
            metrics['max_acceleration'] = accelerations.max()
            metrics['min_acceleration'] = accelerations.min()
            
            # 急加速和急减速事件
            harsh_accel_threshold = 2.0  # m/s²
            harsh_decel_threshold = -2.0  # m/s²
            
            metrics['harsh_accelerations'] = len(accelerations[accelerations > harsh_accel_threshold])
            metrics['harsh_decelerations'] = len(accelerations[accelerations < harsh_decel_threshold])
            
            # 速度变化频率
            metrics['speed_changes'] = len(df[abs(df['speed'].diff()) > 5])
        
        # 行驶距离和时间
        if 'distance' in df.columns:
            metrics['total_distance'] = df['distance'].sum() / 1000  # 转换为公里
        
        if 'timestamp' in df.columns and len(df) > 1:
            metrics['total_time'] = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600  # 小时
        
        # 停车分析
        if 'speed' in df.columns:
            stop_threshold = 5  # km/h
            stopped_records = df[df['speed'] < stop_threshold]
            metrics['stop_rate'] = len(stopped_records) / len(df) * 100
            
            # 计算停车次数（连续停车算作一次）
            is_stopped = df['speed'] < stop_threshold
            stop_changes = is_stopped.diff()
            metrics['stop_events'] = len(stop_changes[stop_changes == True])
        
        return metrics
    
    def _show_behavior_overview(self, metrics):
        """显示行为指标概览"""
        st.subheader("📊 驾驶行为概览")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("平均速度", f"{metrics['avg_speed']:.1f} km/h")
            st.metric("最高速度", f"{metrics['max_speed']:.1f} km/h")
        
        with col2:
            st.metric("超速事件", f"{metrics['speeding_events']} 次")
            st.metric("超速率", f"{metrics['speeding_rate']:.1f}%")
        
        with col3:
            if 'harsh_accelerations' in metrics:
                st.metric("急加速次数", f"{metrics['harsh_accelerations']} 次")
                st.metric("急减速次数", f"{metrics['harsh_decelerations']} 次")
        
        with col4:
            if 'stop_rate' in metrics:
                st.metric("停车率", f"{metrics['stop_rate']:.1f}%")
            if 'stop_events' in metrics:
                st.metric("停车次数", f"{metrics['stop_events']} 次")
    
    def _show_detailed_analysis(self, df, metrics):
        """显示详细分析"""
        st.subheader("📈 详细行为分析")
        
        # 创建标签页
        tab1, tab2, tab3, tab4 = st.tabs(["速度分析", "加速度分析", "行驶模式", "异常行为"])
        
        with tab1:
            self._show_speed_analysis(df, metrics)
        
        with tab2:
            self._show_acceleration_analysis(df, metrics)
        
        with tab3:
            self._show_driving_patterns(df, metrics)
        
        with tab4:
            self._show_anomaly_detection(df, metrics)
    
    def _show_speed_analysis(self, df, metrics):
        """显示速度分析"""
        st.subheader("🏃 速度行为分析")
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 速度分布直方图
            fig_hist = px.histogram(
                df, 
                x='speed', 
                nbins=50,
                title="速度分布",
                labels={'speed': '速度 (km/h)', 'count': '频次'}
            )
            
            # 添加速度限制线
            speed_limit = st.session_state.get('speed_limit', 60)
            fig_hist.add_vline(x=speed_limit, line_dash="dash", line_color="red", 
                              annotation_text=f"速度限制: {speed_limit} km/h")
            
            st.plotly_chart(fig_hist, use_container_width=True)
        
        with col2:
            # 速度箱线图
            fig_box = px.box(
                df, 
                y='speed',
                title="速度分布箱线图",
                labels={'speed': '速度 (km/h)'}
            )
            st.plotly_chart(fig_box, use_container_width=True)
        
        # 速度时间序列（采样）
        if 'timestamp' in df.columns:
            sample_df = df.sample(min(1000, len(df))).sort_values('timestamp')
            
            fig_time = px.line(
                sample_df,
                x='timestamp',
                y='speed',
                title="速度时间序列（采样数据）",
                labels={'timestamp': '时间', 'speed': '速度 (km/h)'}
            )
            
            fig_time.add_hline(y=speed_limit, line_dash="dash", line_color="red")
            st.plotly_chart(fig_time, use_container_width=True)
    
    def _show_acceleration_analysis(self, df, metrics):
        """显示加速度分析"""
        st.subheader("⚡ 加速度行为分析")
        
        if 'timestamp' not in df.columns or len(df) <= 1:
            st.warning("⚠️ 缺少时间戳字段或数据不足，无法进行加速度分析")
            return
        
        # 计算加速度
        speed_changes = df['speed'].diff() / 3.6  # 转换为m/s
        time_diffs = df['timestamp'].diff().dt.total_seconds()
        accelerations = speed_changes / time_diffs
        
        # 过滤异常值
        valid_accelerations = accelerations[abs(accelerations) < 10]
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 加速度分布
            fig_accel = px.histogram(
                x=valid_accelerations,
                nbins=50,
                title="加速度分布",
                labels={'x': '加速度 (m/s²)', 'count': '频次'}
            )
            
            # 添加阈值线
            fig_accel.add_vline(x=2, line_dash="dash", line_color="red", 
                               annotation_text="急加速阈值")
            fig_accel.add_vline(x=-2, line_dash="dash", line_color="red", 
                               annotation_text="急减速阈值")
            
            st.plotly_chart(fig_accel, use_container_width=True)
        
        with col2:
            # 加速度统计
            st.metric("平均加速度", f"{metrics.get('avg_acceleration', 0):.2f} m/s²")
            st.metric("最大加速度", f"{metrics.get('max_acceleration', 0):.2f} m/s²")
            st.metric("最大减速度", f"{metrics.get('min_acceleration', 0):.2f} m/s²")
            
            # 急加速/减速统计
            harsh_accel = metrics.get('harsh_accelerations', 0)
            harsh_decel = metrics.get('harsh_decelerations', 0)
            
            st.metric("急加速事件", f"{harsh_accel} 次")
            st.metric("急减速事件", f"{harsh_decel} 次")
    
    def _show_driving_patterns(self, df, metrics):
        """显示行驶模式分析"""
        st.subheader("🛣️ 行驶模式分析")
        
        # 速度区间分析
        speed_ranges = {
            '低速 (0-30 km/h)': (0, 30),
            '中速 (30-60 km/h)': (30, 60),
            '高速 (60-90 km/h)': (60, 90),
            '超高速 (>90 km/h)': (90, float('inf'))
        }
        
        range_stats = {}
        for range_name, (min_speed, max_speed) in speed_ranges.items():
            if max_speed == float('inf'):
                count = len(df[df['speed'] > min_speed])
            else:
                count = len(df[(df['speed'] >= min_speed) & (df['speed'] < max_speed)])
            
            range_stats[range_name] = {
                'count': count,
                'percentage': count / len(df) * 100
            }
        
        # 显示速度区间分布
        col1, col2 = st.columns(2)
        
        with col1:
            # 饼图
            labels = list(range_stats.keys())
            values = [stats['count'] for stats in range_stats.values()]
            
            fig_pie = px.pie(
                values=values,
                names=labels,
                title="行驶速度区间分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # 柱状图
            percentages = [stats['percentage'] for stats in range_stats.values()]
            
            fig_bar = px.bar(
                x=labels,
                y=percentages,
                title="各速度区间占比",
                labels={'x': '速度区间', 'y': '占比 (%)'}
            )
            st.plotly_chart(fig_bar, use_container_width=True)
        
        # 行驶效率分析
        if 'total_distance' in metrics and 'total_time' in metrics:
            st.subheader("📊 行驶效率")
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("总距离", f"{metrics['total_distance']:.2f} km")
            
            with col2:
                st.metric("总时间", f"{metrics['total_time']:.2f} 小时")
            
            with col3:
                if metrics['total_time'] > 0:
                    avg_speed = metrics['total_distance'] / metrics['total_time']
                    st.metric("平均行驶速度", f"{avg_speed:.1f} km/h")
    
    def _show_anomaly_detection(self, df, metrics):
        """显示异常行为检测"""
        st.subheader("🚨 异常行为检测")
        
        anomalies = []
        
        # 检测异常高速
        speed_threshold = df['speed'].quantile(0.95)
        high_speed_anomalies = df[df['speed'] > speed_threshold]
        if len(high_speed_anomalies) > 0:
            anomalies.append({
                'type': '异常高速',
                'count': len(high_speed_anomalies),
                'description': f'速度超过95%分位数 ({speed_threshold:.1f} km/h)',
                'severity': 'high' if speed_threshold > 80 else 'medium'
            })
        
        # 检测速度突变
        if len(df) > 1:
            speed_changes = abs(df['speed'].diff())
            sudden_changes = speed_changes[speed_changes > 20]  # 速度变化超过20km/h
            if len(sudden_changes) > 0:
                anomalies.append({
                    'type': '速度突变',
                    'count': len(sudden_changes),
                    'description': '速度变化超过20 km/h',
                    'severity': 'medium'
                })
        
        # 检测长时间高速
        if 'timestamp' in df.columns:
            high_speed_threshold = st.session_state.get('speed_limit', 60) + 20
            high_speed_periods = df[df['speed'] > high_speed_threshold]
            if len(high_speed_periods) > 10:  # 连续10个点以上
                anomalies.append({
                    'type': '长时间高速',
                    'count': len(high_speed_periods),
                    'description': f'长时间保持高速 (>{high_speed_threshold} km/h)',
                    'severity': 'high'
                })
        
        # 显示异常检测结果
        if anomalies:
            for anomaly in anomalies:
                severity_color = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }
                
                st.warning(f"{severity_color[anomaly['severity']]} **{anomaly['type']}**: "
                          f"{anomaly['count']} 次 - {anomaly['description']}")
        else:
            st.success("✅ 未检测到明显的异常驾驶行为")
    
    def _show_behavior_scoring(self, metrics):
        """显示驾驶行为评分"""
        st.subheader("⭐ 驾驶行为评分")
        
        # 计算各项评分
        scores = {}
        
        # 超速评分 (0-100)
        speeding_rate = metrics.get('speeding_rate', 0)
        scores['超速控制'] = max(0, 100 - speeding_rate * 2)
        
        # 平稳驾驶评分
        harsh_events = metrics.get('harsh_accelerations', 0) + metrics.get('harsh_decelerations', 0)
        total_records = metrics.get('total_records', 1)
        harsh_rate = harsh_events / total_records * 100
        scores['平稳驾驶'] = max(0, 100 - harsh_rate * 10)
        
        # 速度稳定性评分
        speed_std = metrics.get('speed_std', 0)
        scores['速度稳定'] = max(0, 100 - speed_std)
        
        # 综合评分
        overall_score = np.mean(list(scores.values()))
        
        # 显示评分
        col1, col2 = st.columns(2)
        
        with col1:
            for score_name, score_value in scores.items():
                st.metric(score_name, f"{score_value:.1f} 分")
        
        with col2:
            # 综合评分仪表盘
            fig_gauge = go.Figure(go.Indicator(
                mode="gauge+number+delta",
                value=overall_score,
                domain={'x': [0, 1], 'y': [0, 1]},
                title={'text': "综合驾驶评分"},
                delta={'reference': 80},
                gauge={
                    'axis': {'range': [None, 100]},
                    'bar': {'color': "darkblue"},
                    'steps': [
                        {'range': [0, 50], 'color': "lightgray"},
                        {'range': [50, 80], 'color': "yellow"},
                        {'range': [80, 100], 'color': "green"}
                    ],
                    'threshold': {
                        'line': {'color': "red", 'width': 4},
                        'thickness': 0.75,
                        'value': 90
                    }
                }
            ))
            
            fig_gauge.update_layout(height=300)
            st.plotly_chart(fig_gauge, use_container_width=True)
        
        # 评分说明
        if overall_score >= 90:
            st.success("🌟 优秀！您的驾驶行为非常规范")
        elif overall_score >= 80:
            st.info("👍 良好！驾驶行为总体较好，有小幅改进空间")
        elif overall_score >= 60:
            st.warning("⚠️ 一般！建议改善超速和急加减速行为")
        else:
            st.error("❌ 需要改进！存在较多不安全驾驶行为")

        # 改进建议
        st.subheader("💡 改进建议")
        suggestions = []

        if scores['超速控制'] < 80:
            suggestions.append("🚗 建议严格遵守速度限制，减少超速行为")

        if scores['平稳驾驶'] < 80:
            suggestions.append("🛣️ 建议减少急加速和急减速，保持平稳驾驶")

        if scores['速度稳定'] < 80:
            suggestions.append("📊 建议保持稳定的行驶速度，避免频繁变速")

        if suggestions:
            for suggestion in suggestions:
                st.info(suggestion)
        else:
            st.success("✅ 您的驾驶行为很好，请继续保持！")
