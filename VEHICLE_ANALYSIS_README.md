# 🚗 车辆轨迹数据分析工具

## 概述

这是一个专业的车辆轨迹数据分析工具，基于Streamlit框架开发，支持大数据量的GPS轨迹数据处理和分析。该工具集成了超速分析、驾驶行为分析、轨迹可视化等多种功能。

## 主要功能

### 📁 数据导入
- **支持格式**: CSV文件
- **智能字段映射**: 自动识别和映射非标准字段名
- **必需字段**: `timestamp`, `latitude`, `longitude`
- **可选字段**: `speed`, `vehicle_id`, `driver_id`, `altitude`, `heading`
- **数据验证**: 自动验证坐标范围、速度合理性等
- **大数据处理**: 支持百万级轨迹点，自动采样优化
- **示例数据**: 内置示例数据生成功能

### 🗺️ 轨迹可视化
- **地图展示**: 基于OpenStreetMap的轨迹可视化
- **速度着色**: 按速度对轨迹点进行颜色编码
- **超速突出**: 可选择仅显示超速轨迹点
- **性能优化**: 支持大数据量的采样显示

### ⚡ 超速分析
- **超速检测**: 自动识别超过限速的轨迹点
- **事件分析**: 检测连续超速事件，分析持续时间和严重程度
- **统计图表**: 超速分布、时间序列分析
- **可配置限速**: 支持自定义速度限制阈值

### 🚗 驾驶行为分析
- **行为指标**: 平均速度、最高速度、急加速/减速次数
- **驾驶评分**: 0-100分的综合驾驶行为评分系统
- **改进建议**: 基于分析结果提供个性化改进建议
- **趋势分析**: 按小时统计的驾驶行为趋势

### 📊 统计报告
- **综合报告**: 完整的数据分析报告
- **数据质量**: 数据完整性和合理性评估
- **安全分析**: 超速、急操作等安全指标统计
- **导出功能**: 支持数据导出（开发中：Excel、PDF报告）

### ⚙️ 高级分析
- **轨迹聚类**: 基于位置的轨迹聚类分析
- **异常检测**: 速度异常和统计异常检测
- **燃油估算**: 基于行驶距离和速度的燃油消耗估算
- **路径优化**: 路径优化分析（开发中）
- **驾驶模式识别**: 驾驶模式自动识别（开发中）

## 数据格式要求

### 标准CSV格式
```csv
timestamp,latitude,longitude,speed,vehicle_id
2024-01-01 08:00:00,39.9042,116.4074,45.5,V001
2024-01-01 08:00:30,39.9045,116.4078,52.3,V001
2024-01-01 08:01:00,39.9048,116.4082,48.7,V001
```

### 字段说明
- **timestamp**: 时间戳（YYYY-MM-DD HH:MM:SS格式）
- **latitude**: 纬度（十进制度数）
- **longitude**: 经度（十进制度数）
- **speed**: 速度（km/h，可选，系统可自动计算）
- **vehicle_id**: 车辆ID（可选，用于多车辆分析）
- **driver_id**: 驾驶员ID（可选）
- **altitude**: 海拔高度（可选）
- **heading**: 方向角（可选）

## 智能字段映射

系统支持智能字段映射，可以自动识别常见的非标准字段名：

### 自动识别规则
- **时间字段**: `datetime`, `time`, `timestamp`, `date_time` → `timestamp`
- **纬度字段**: `lat`, `latitude`, `纬度` → `latitude`
- **经度字段**: `lng`, `lon`, `longitude`, `经度` → `longitude`
- **速度字段**: `velocity`, `speed`, `vel`, `速度` → `speed`
- **车辆字段**: `vehicle`, `car_id`, `vehicle_id`, `车辆ID` → `vehicle_id`
- **驾驶员字段**: `driver`, `driver_id`, `driver_name`, `驾驶员` → `driver_id`

### 映射示例
如果您的CSV文件字段为：
```csv
datetime,lat,lng,velocity,car_id,driver_name
```

系统会自动建议映射为：
- `datetime` → `timestamp`
- `lat` → `latitude`
- `lng` → `longitude`
- `velocity` → `speed`
- `car_id` → `vehicle_id`
- `driver_name` → `driver_id`

## 使用指南

### 1. 数据导入
1. 点击左侧导航栏的"🚗 车辆轨迹分析"
2. 在"📁 数据导入"标签页中上传CSV文件
3. **字段映射配置**：
   - 系统自动识别CSV文件的字段
   - 将您的字段映射到标准字段（如：datetime→timestamp, lat→latitude）
   - 红色标记为必需字段，黄色为可选字段
   - 查看映射预览确认无误
4. 设置采样率和速度限制参数
5. 点击"开始处理数据"按钮
6. 查看数据质量报告和处理结果

### 2. 轨迹可视化
1. 切换到"🗺️ 轨迹可视化"标签页
2. 选择可视化选项（速度着色、超速点等）
3. 调整显示点数以优化性能
4. 查看轨迹地图和统计信息

### 3. 超速分析
1. 在"⚡ 超速分析"标签页查看超速统计
2. 分析超速事件的分布和特征
3. 查看速度分布直方图和时间序列

### 4. 驾驶行为评估
1. 在"🚗 驾驶行为分析"标签页查看行为指标
2. 查看驾驶评分和改进建议
3. 分析驾驶行为的时间趋势

### 5. 生成报告
1. 在"📊 统计报告"标签页查看综合分析
2. 评估数据质量和安全指标
3. 导出处理后的数据

## 性能优化

### 大数据处理
- **采样机制**: 自动采样减少内存使用
- **分批处理**: 避免内存溢出
- **缓存机制**: 使用session state缓存数据
- **可视化优化**: 限制地图显示点数

### 建议配置
- **文件大小**: 建议单文件不超过500MB
- **采样率**: 大文件建议使用50-80%采样率
- **显示点数**: 地图可视化建议不超过5000点

## 技术架构

### 核心技术栈
- **前端框架**: Streamlit
- **数据处理**: Pandas, NumPy
- **可视化**: Plotly, Plotly Express
- **地图展示**: OpenStreetMap
- **机器学习**: Scikit-learn（可选）
- **统计分析**: SciPy（可选）

### 模块结构
```
module-pages/
├── vehicle_trajectory_analysis.py  # 主分析模块
├── dashboard.py                     # 仪表板
├── data_analysis.py                 # 通用数据分析
├── charts.py                        # 图表展示
├── reports.py                       # 报告中心
├── tools.py                         # 实用工具
└── settings.py                      # 系统设置
```

## 扩展功能

### 已实现
- ✅ 基础轨迹分析
- ✅ 超速检测和分析
- ✅ 驾驶行为评估
- ✅ 轨迹可视化
- ✅ 统计报告生成

### 开发中
- 🚧 Excel/PDF报告导出
- 🚧 多车辆对比分析
- 🚧 路径优化算法
- 🚧 驾驶模式识别
- 🚧 实时数据接入

### 计划中
- 📋 历史数据对比
- 📋 预测性分析
- 📋 告警系统
- 📋 API接口
- 📋 移动端适配

## 故障排除

### 常见问题
1. **数据导入失败**: 检查CSV格式和必需字段
2. **地图不显示**: 检查网络连接和坐标范围
3. **性能问题**: 降低采样率或显示点数
4. **内存不足**: 使用更小的数据文件或增加采样

### 依赖问题
如果遇到导入错误，请确保安装了所有依赖：
```bash
uv sync  # 或 pip install -r requirements.txt
```

## 联系支持

如有问题或建议，请通过以下方式联系：
- 📧 技术支持: [待添加]
- 📖 文档更新: [待添加]
- 🐛 问题反馈: [待添加]

---

**版本**: v1.0.0  
**更新日期**: 2024-06-27  
**兼容性**: Python 3.11+, Streamlit 1.46+
