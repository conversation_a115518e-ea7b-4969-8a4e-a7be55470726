import panel as pn
import param

# 启用Panel扩展
pn.extension('tabulator', 'bokeh')

class AppTemplate(param.Parameterized):
    """Panel应用程序模板，包含左侧工具栏和右侧页面切换功能"""
    
    current_page = param.String(default="dashboard", doc="当前显示的页面")
    
    def __init__(self, **params):
        super().__init__(**params)
        self.setup_pages()
        self.setup_toolbar()
        self.setup_layout()
    
    def setup_pages(self):
        """设置各个页面的内容"""
        self.pages = {
            "dashboard": self.create_dashboard_page(),
            "data_analysis": self.create_data_analysis_page(),
            "charts": self.create_charts_page(),
            "settings": self.create_settings_page(),
            "reports": self.create_reports_page(),
            "tools": self.create_tools_page(),
        }
    
    def create_dashboard_page(self):
        """创建仪表板页面"""
        return pn.Column(
            pn.pane.Markdown("# 仪表板", sizing_mode="stretch_width"),
            pn.pane.Markdown("""
            欢迎来到应用程序仪表板！
            
            这里可以显示：
            - 关键指标概览
            - 最近活动
            - 快速操作按钮
            """),
            pn.Row(
                pn.indicators.Number(
                    name="总用户数", value=1234, format="{value:,}",
                    background="lightblue", width=200, height=100
                ),
                pn.indicators.Number(
                    name="活跃用户", value=567, format="{value:,}",
                    background="lightgreen", width=200, height=100
                ),
                pn.indicators.Number(
                    name="收入", value=89012, format="${value:,.0f}",
                    background="lightyellow", width=200, height=100
                ),
            ),
            sizing_mode="stretch_width"
        )
    
    def create_data_analysis_page(self):
        """创建数据分析页面"""
        return pn.Column(
            pn.pane.Markdown("# 数据分析", sizing_mode="stretch_width"),
            pn.pane.Markdown("这里可以进行各种数据分析操作..."),
            pn.widgets.FileInput(accept='.csv,.xlsx', multiple=True),
            pn.pane.Markdown("### 数据预览"),
            pn.pane.HTML("<div style='height: 300px; background: #f0f0f0; padding: 20px;'>数据表格将在这里显示</div>"),
            sizing_mode="stretch_width"
        )
    
    def create_charts_page(self):
        """创建图表页面"""
        import numpy as np
        from bokeh.plotting import figure
        
        # 创建示例图表
        x = np.linspace(0, 4*np.pi, 100)
        y = np.sin(x)
        
        p = figure(title="示例图表", width=600, height=400)
        p.line(x, y, legend_label="sin(x)", line_width=2)
        
        return pn.Column(
            pn.pane.Markdown("# 图表展示", sizing_mode="stretch_width"),
            pn.pane.Bokeh(p),
            pn.Row(
                pn.widgets.Select(name="图表类型", options=["折线图", "柱状图", "散点图"], value="折线图"),
                pn.widgets.DateRangeSlider(name="日期范围"),
            ),
            sizing_mode="stretch_width"
        )
    
    def create_settings_page(self):
        """创建设置页面"""
        return pn.Column(
            pn.pane.Markdown("# 系统设置", sizing_mode="stretch_width"),
            pn.Accordion(
                ("用户设置", pn.Column(
                    pn.widgets.TextInput(name="用户名", placeholder="输入用户名"),
                    pn.widgets.PasswordInput(name="密码", placeholder="输入密码"),
                    pn.widgets.Select(name="语言", options=["中文", "English"], value="中文"),
                )),
                ("系统配置", pn.Column(
                    pn.widgets.Checkbox(name="启用通知"),
                    pn.widgets.Checkbox(name="自动保存"),
                    pn.widgets.IntSlider(name="刷新间隔(秒)", start=1, end=60, value=10),
                )),
                ("数据库设置", pn.Column(
                    pn.widgets.TextInput(name="数据库URL", placeholder="输入数据库连接地址"),
                    pn.widgets.IntInput(name="连接池大小", value=10),
                    pn.widgets.Button(name="测试连接", button_type="primary"),
                )),
                active=[0],  # 默认展开第一个
            ),
            sizing_mode="stretch_width"
        )
    
    def create_reports_page(self):
        """创建报告页面"""
        return pn.Column(
            pn.pane.Markdown("# 报告中心", sizing_mode="stretch_width"),
            pn.Row(
                pn.widgets.DatePicker(name="开始日期"),
                pn.widgets.DatePicker(name="结束日期"),
                pn.widgets.Button(name="生成报告", button_type="primary"),
            ),
            pn.pane.Markdown("### 可用报告"),
            pn.Column(
                pn.Card(
                    pn.pane.Markdown("**销售报告**\n\n月度销售数据汇总"),
                    pn.widgets.Button(name="下载", button_type="outline"),
                    title="销售报告",
                    collapsible=False,
                ),
                pn.Card(
                    pn.pane.Markdown("**用户分析报告**\n\n用户行为和趋势分析"),
                    pn.widgets.Button(name="下载", button_type="outline"),
                    title="用户分析报告",
                    collapsible=False,
                ),
            ),
            sizing_mode="stretch_width"
        )
    
    def create_tools_page(self):
        """创建工具页面"""
        return pn.Column(
            pn.pane.Markdown("# 实用工具", sizing_mode="stretch_width"),
            pn.GridBox(
                pn.Card(
                    pn.pane.Markdown("**数据导入工具**"),
                    pn.widgets.Button(name="启动", button_type="primary"),
                    title="数据导入",
                ),
                pn.Card(
                    pn.pane.Markdown("**数据清洗工具**"),
                    pn.widgets.Button(name="启动", button_type="primary"),
                    title="数据清洗",
                ),
                pn.Card(
                    pn.pane.Markdown("**批量处理工具**"),
                    pn.widgets.Button(name="启动", button_type="primary"),
                    title="批量处理",
                ),
                pn.Card(
                    pn.pane.Markdown("**API测试工具**"),
                    pn.widgets.Button(name="启动", button_type="primary"),
                    title="API测试",
                ),
                ncols=2,
            ),
            sizing_mode="stretch_width"
        )
    
    def setup_toolbar(self):
        """设置左侧工具栏"""
        # 工具栏按钮组
        toolbar_groups = {
            "主要功能": [
                ("📊 仪表板", "dashboard"),
                ("📈 数据分析", "data_analysis"),
                ("📉 图表", "charts"),
            ],
            "管理": [
                ("📋 报告", "reports"),
                ("🔧 工具", "tools"),
                ("⚙️ 设置", "settings"),
            ]
        }
        
        self.toolbar_buttons = {}
        toolbar_sections = []
        
        for group_name, buttons in toolbar_groups.items():
            # 创建组标题
            group_title = pn.pane.Markdown(f"**{group_name}**", margin=(10, 5, 5, 5))
            toolbar_sections.append(group_title)
            
            # 创建组内按钮
            for button_text, page_key in buttons:
                btn = pn.widgets.Button(
                    name=button_text,
                    button_type="outline",
                    width=180,
                    margin=(2, 5),
                )
                btn.param.watch(lambda event, pk=page_key: self.switch_page(pk), 'clicks')
                self.toolbar_buttons[page_key] = btn
                toolbar_sections.append(btn)
        
        self.toolbar = pn.Column(
            pn.pane.Markdown("## 导航菜单", margin=(10, 5)),
            *toolbar_sections,
            width=200,
            background="whitesmoke",
            margin=(0, 10, 0, 0),
        )
        
        # 设置默认选中状态
        self.update_button_styles()
    
    def switch_page(self, page_key):
        """切换页面"""
        self.current_page = page_key
        self.update_button_styles()
        self.main_content.objects = [self.pages[page_key]]
    
    def update_button_styles(self):
        """更新按钮样式以显示当前选中状态"""
        for page_key, button in self.toolbar_buttons.items():
            if page_key == self.current_page:
                button.button_type = "primary"
            else:
                button.button_type = "outline"
    
    def setup_layout(self):
        """设置整体布局"""
        # 主内容区域
        self.main_content = pn.Column(
            self.pages[self.current_page],
            sizing_mode="stretch_both",
            margin=(0, 0, 0, 10),
        )
        
        # 整体布局
        self.layout = pn.Row(
            self.toolbar,
            self.main_content,
            sizing_mode="stretch_both",
            height=800,
        )
    
    def servable(self):
        """返回可服务的Panel对象"""
        return self.layout


def create_app():
    """创建并返回应用程序实例"""
    app = AppTemplate()
    return app.servable()


if __name__ == "__main__":
    # 创建应用程序
    app = create_app()
    
    # 设置页面标题
    pn.config.title = "Panel应用程序模板"
    
    # 启动服务器
    app.show(port=5007, autoreload=True)
