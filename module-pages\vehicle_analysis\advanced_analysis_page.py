"""
高级分析页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from .base_page import BasePage, UtilityFunctions


class AdvancedAnalysisPage(BasePage):
    """高级分析页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "⚙️ 高级分析"
        self.description = "车辆轨迹数据高级分析功能"
    
    def render(self):
        """渲染高级分析页面"""
        st.subheader(self.title)
        
        if not self.has_data():
            st.warning("⚠️ 请先在'数据导入'页面上传数据")
            return
        
        df = self.get_data()
        
        # 显示数据信息
        self.show_data_info(df)
        
        # 高级分析选项
        analysis_type = st.selectbox(
            "选择分析类型",
            ["轨迹聚类分析", "异常检测", "路径优化分析", "驾驶模式识别", "预测分析"]
        )
        
        if analysis_type == "轨迹聚类分析":
            self._trajectory_clustering_analysis(df)
        elif analysis_type == "异常检测":
            self._anomaly_detection_analysis(df)
        elif analysis_type == "路径优化分析":
            self._route_optimization_analysis(df)
        elif analysis_type == "驾驶模式识别":
            self._driving_pattern_recognition(df)
        elif analysis_type == "预测分析":
            self._predictive_analysis(df)
    
    def _trajectory_clustering_analysis(self, df):
        """轨迹聚类分析"""
        st.subheader("🎯 轨迹聚类分析")
        
        if not self.validate_required_fields(df, ['latitude', 'longitude']):
            return
        
        # 聚类参数设置
        col1, col2 = st.columns(2)
        
        with col1:
            n_clusters = st.slider("聚类数量", 2, 10, 5)
        
        with col2:
            sample_size = st.slider("采样数量", 100, min(5000, len(df)), min(1000, len(df)))
        
        if st.button("🚀 开始聚类分析"):
            with st.spinner("正在进行聚类分析..."):
                # 数据采样
                sample_df = df.sample(n=sample_size) if len(df) > sample_size else df
                
                # 使用简化的聚类方法（避免sklearn依赖）
                try:
                    from sklearn.cluster import KMeans
                    coords = sample_df[['latitude', 'longitude']].values
                    kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                    clusters = kmeans.fit_predict(coords)
                    
                    sample_df = sample_df.copy()
                    sample_df['cluster'] = clusters
                    
                    # 可视化聚类结果
                    fig = px.scatter_mapbox(
                        sample_df,
                        lat='latitude',
                        lon='longitude',
                        color='cluster',
                        title=f"轨迹聚类结果 ({n_clusters} 个聚类)",
                        mapbox_style="open-street-map",
                        height=600,
                        color_continuous_scale='viridis'
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
                    
                    # 聚类统计
                    cluster_stats = sample_df.groupby('cluster').agg({
                        'latitude': ['count', 'mean'],
                        'longitude': 'mean',
                        'speed': 'mean' if 'speed' in sample_df.columns else lambda x: 0
                    }).round(4)
                    
                    st.subheader("📊 聚类统计")
                    st.dataframe(cluster_stats, use_container_width=True)
                    
                except ImportError:
                    st.error("❌ 需要安装scikit-learn库才能使用聚类功能")
                    st.code("pip install scikit-learn")
                    
                    # 提供简化的区域分析
                    st.info("💡 使用简化的区域分析代替聚类")
                    self._simple_region_analysis(sample_df)
    
    def _simple_region_analysis(self, df):
        """简化的区域分析"""
        # 基于经纬度范围划分区域
        lat_bins = pd.cut(df['latitude'], bins=3, labels=['南部', '中部', '北部'])
        lon_bins = pd.cut(df['longitude'], bins=3, labels=['西部', '中部', '东部'])
        
        df = df.copy()
        df['region'] = lat_bins.astype(str) + '-' + lon_bins.astype(str)
        
        # 区域统计
        region_stats = df.groupby('region').agg({
            'latitude': 'count',
            'speed': 'mean' if 'speed' in df.columns else lambda x: 0
        }).round(2)
        
        region_stats.columns = ['数据点数', '平均速度']
        st.dataframe(region_stats, use_container_width=True)
        
        # 区域分布图
        fig = px.scatter_mapbox(
            df,
            lat='latitude',
            lon='longitude',
            color='region',
            title="区域分布分析",
            mapbox_style="open-street-map",
            height=500
        )
        st.plotly_chart(fig, use_container_width=True)
    
    def _anomaly_detection_analysis(self, df):
        """异常检测分析"""
        st.subheader("🔍 异常检测分析")
        
        # 异常检测方法选择
        detection_method = st.selectbox(
            "选择检测方法",
            ["统计方法", "速度异常", "位置异常", "时间异常"]
        )
        
        if detection_method == "统计方法":
            self._statistical_anomaly_detection(df)
        elif detection_method == "速度异常":
            self._speed_anomaly_detection(df)
        elif detection_method == "位置异常":
            self._location_anomaly_detection(df)
        elif detection_method == "时间异常":
            self._time_anomaly_detection(df)
    
    def _statistical_anomaly_detection(self, df):
        """统计方法异常检测"""
        if 'speed' not in df.columns:
            st.warning("⚠️ 缺少速度字段")
            return
        
        # Z-score异常检测
        threshold = st.slider("Z-score阈值", 1.0, 5.0, 3.0, 0.1)
        
        if st.button("🔍 检测异常"):
            # Z-score异常检测（使用numpy实现）
            try:
                from scipy import stats
                z_scores = np.abs(stats.zscore(df['speed']))
            except ImportError:
                # 如果没有scipy，手动计算z-score
                mean_speed = df['speed'].mean()
                std_speed = df['speed'].std()
                z_scores = np.abs((df['speed'] - mean_speed) / std_speed)
            
            anomalies = df[z_scores > threshold].copy()
            anomalies['z_score'] = z_scores[z_scores > threshold]
            
            st.success(f"✅ 检测到 {len(anomalies)} 个异常点")
            
            if len(anomalies) > 0:
                # 显示异常点
                col1, col2 = st.columns(2)
                
                with col1:
                    st.subheader("📋 异常数据")
                    st.dataframe(anomalies[['timestamp', 'speed', 'z_score']].head(20))
                
                with col2:
                    # 异常点可视化
                    fig = px.scatter(
                        df,
                        x=range(len(df)),
                        y='speed',
                        title="速度异常检测",
                        labels={'x': '数据点索引', 'y': '速度 (km/h)'}
                    )
                    
                    # 添加异常点
                    anomaly_indices = anomalies.index
                    fig.add_scatter(
                        x=anomaly_indices,
                        y=anomalies['speed'],
                        mode='markers',
                        marker=dict(color='red', size=8),
                        name='异常点'
                    )
                    
                    st.plotly_chart(fig, use_container_width=True)
    
    def _speed_anomaly_detection(self, df):
        """速度异常检测"""
        if 'speed' not in df.columns:
            st.warning("⚠️ 缺少速度字段")
            return
        
        col1, col2 = st.columns(2)
        
        with col1:
            min_speed = st.number_input("最低合理速度 (km/h)", 0, 50, 0)
            max_speed = st.number_input("最高合理速度 (km/h)", 50, 200, 120)
        
        with col2:
            sudden_change_threshold = st.number_input("速度突变阈值 (km/h)", 10, 50, 20)
        
        if st.button("🔍 检测速度异常"):
            # 检测超出范围的速度
            speed_outliers = df[(df['speed'] < min_speed) | (df['speed'] > max_speed)]
            
            # 检测速度突变
            speed_changes = abs(df['speed'].diff())
            sudden_changes = df[speed_changes > sudden_change_threshold]
            
            st.subheader("📊 异常检测结果")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("速度超出范围", f"{len(speed_outliers)} 个点")
                if len(speed_outliers) > 0:
                    st.dataframe(speed_outliers[['timestamp', 'speed']].head(10))
            
            with col2:
                st.metric("速度突变", f"{len(sudden_changes)} 个点")
                if len(sudden_changes) > 0:
                    st.dataframe(sudden_changes[['timestamp', 'speed']].head(10))
    
    def _location_anomaly_detection(self, df):
        """位置异常检测"""
        if not self.validate_required_fields(df, ['latitude', 'longitude']):
            return
        
        # 检测坐标范围异常
        invalid_coords = df[
            (df['latitude'] < -90) | (df['latitude'] > 90) |
            (df['longitude'] < -180) | (df['longitude'] > 180)
        ]
        
        # 检测位置跳跃
        if len(df) > 1:
            distances = []
            for i in range(1, len(df)):
                dist = UtilityFunctions.calculate_distance(
                    df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                    df.iloc[i]['latitude'], df.iloc[i]['longitude']
                )
                distances.append(dist)
            
            df_with_dist = df[1:].copy()
            df_with_dist['distance_jump'] = distances
            
            jump_threshold = st.number_input("位置跳跃阈值 (米)", 100, 10000, 1000)
            
            if st.button("🔍 检测位置异常"):
                location_jumps = df_with_dist[df_with_dist['distance_jump'] > jump_threshold]
                
                st.subheader("📊 位置异常检测结果")
                
                col1, col2 = st.columns(2)
                
                with col1:
                    st.metric("无效坐标", f"{len(invalid_coords)} 个点")
                    st.metric("位置跳跃", f"{len(location_jumps)} 个点")
                
                with col2:
                    if len(location_jumps) > 0:
                        st.dataframe(location_jumps[['timestamp', 'latitude', 'longitude', 'distance_jump']].head(10))
    
    def _time_anomaly_detection(self, df):
        """时间异常检测"""
        if 'timestamp' not in df.columns:
            st.warning("⚠️ 缺少时间戳字段")
            return
        
        # 检测时间间隔异常
        time_diffs = df['timestamp'].diff().dt.total_seconds()
        
        col1, col2 = st.columns(2)
        
        with col1:
            min_interval = st.number_input("最小时间间隔 (秒)", 1, 60, 5)
            max_interval = st.number_input("最大时间间隔 (秒)", 60, 3600, 300)
        
        if st.button("🔍 检测时间异常"):
            # 检测时间间隔异常
            time_anomalies = df[1:][(time_diffs[1:] < min_interval) | (time_diffs[1:] > max_interval)]
            
            # 检测重复时间戳
            duplicate_times = df[df['timestamp'].duplicated()]
            
            st.subheader("📊 时间异常检测结果")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.metric("时间间隔异常", f"{len(time_anomalies)} 个点")
                st.metric("重复时间戳", f"{len(duplicate_times)} 个点")
            
            with col2:
                if len(time_anomalies) > 0:
                    time_anomalies_with_diff = time_anomalies.copy()
                    time_anomalies_with_diff['time_diff'] = time_diffs[time_anomalies.index]
                    st.dataframe(time_anomalies_with_diff[['timestamp', 'time_diff']].head(10))
    
    def _route_optimization_analysis(self, df):
        """路径优化分析"""
        st.subheader("🛣️ 路径优化分析")
        
        if not self.validate_required_fields(df, ['latitude', 'longitude']):
            return
        
        st.info("💡 路径优化分析功能")
        st.markdown("""
        **分析内容：**
        - 路径效率评估
        - 绕行检测
        - 最优路径建议
        - 燃油消耗估算
        """)
        
        # 简化的路径分析
        if len(df) > 1:
            # 计算总距离
            total_distance = df['distance'].sum() / 1000 if 'distance' in df.columns else 0
            
            # 计算直线距离
            start_point = (df.iloc[0]['latitude'], df.iloc[0]['longitude'])
            end_point = (df.iloc[-1]['latitude'], df.iloc[-1]['longitude'])
            straight_distance = UtilityFunctions.calculate_distance(
                start_point[0], start_point[1], end_point[0], end_point[1]
            ) / 1000
            
            # 计算绕行率
            if straight_distance > 0:
                detour_rate = ((total_distance - straight_distance) / straight_distance) * 100
            else:
                detour_rate = 0
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("实际距离", f"{total_distance:.2f} km")
            
            with col2:
                st.metric("直线距离", f"{straight_distance:.2f} km")
            
            with col3:
                st.metric("绕行率", f"{detour_rate:.1f}%")
            
            # 路径效率评估
            if detour_rate > 50:
                st.warning("⚠️ 路径绕行较多，建议优化")
            elif detour_rate > 20:
                st.info("💡 路径效率一般，有优化空间")
            else:
                st.success("✅ 路径效率较高")
    
    def _driving_pattern_recognition(self, df):
        """驾驶模式识别"""
        st.subheader("🚗 驾驶模式识别")
        
        if 'speed' not in df.columns:
            st.warning("⚠️ 缺少速度字段")
            return
        
        # 驾驶模式分类
        patterns = self._classify_driving_patterns(df)
        
        # 显示模式分布
        col1, col2 = st.columns(2)
        
        with col1:
            # 饼图显示模式分布
            fig_pie = px.pie(
                values=list(patterns.values()),
                names=list(patterns.keys()),
                title="驾驶模式分布"
            )
            st.plotly_chart(fig_pie, use_container_width=True)
        
        with col2:
            # 模式统计
            for pattern, count in patterns.items():
                percentage = (count / len(df)) * 100
                st.metric(pattern, f"{count} 次 ({percentage:.1f}%)")
    
    def _classify_driving_patterns(self, df):
        """分类驾驶模式"""
        patterns = {
            '城市驾驶': 0,
            '高速驾驶': 0,
            '停车等待': 0,
            '缓慢行驶': 0
        }
        
        for _, row in df.iterrows():
            speed = row['speed']
            
            if speed < 5:
                patterns['停车等待'] += 1
            elif speed < 30:
                patterns['缓慢行驶'] += 1
            elif speed < 60:
                patterns['城市驾驶'] += 1
            else:
                patterns['高速驾驶'] += 1
        
        return patterns
    
    def _predictive_analysis(self, df):
        """预测分析"""
        st.subheader("🔮 预测分析")
        
        st.info("💡 预测分析功能（演示版）")
        st.markdown("""
        **可预测内容：**
        - 未来速度趋势
        - 燃油消耗预测
        - 维护需求预测
        - 路径时间预估
        """)
        
        if 'speed' in df.columns and 'timestamp' in df.columns:
            # 简单的趋势预测
            recent_data = df.tail(100)  # 最近100个数据点
            
            if len(recent_data) > 10:
                # 计算速度趋势
                speed_trend = np.polyfit(range(len(recent_data)), recent_data['speed'], 1)[0]
                
                col1, col2 = st.columns(2)
                
                with col1:
                    if speed_trend > 0.1:
                        st.success("📈 速度呈上升趋势")
                    elif speed_trend < -0.1:
                        st.warning("📉 速度呈下降趋势")
                    else:
                        st.info("➡️ 速度保持稳定")
                
                with col2:
                    avg_speed = recent_data['speed'].mean()
                    predicted_fuel = avg_speed * 0.08  # 简化的燃油消耗模型
                    st.metric("预测燃油消耗", f"{predicted_fuel:.2f} L/100km")
        
        st.markdown("---")
        st.info("💡 完整的预测分析功能需要更多历史数据和机器学习模型支持")
