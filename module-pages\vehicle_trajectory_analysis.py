"""
车辆轨迹分析主模块 - 面向对象重构版本
"""
import streamlit as st
import warnings
warnings.filterwarnings('ignore')

# 导入各个页面类
from vehicle_analysis import (
    DataImportPage,
    TrajectoryVisualizationPage,
    SpeedingAnalysisPage,
    DrivingBehaviorPage,
    StatisticsReportPage,
    AdvancedAnalysisPage
)

# 页面类实例化
page_instances = {
    'data_import': DataImportPage(),
    'trajectory_visualization': TrajectoryVisualizationPage(),
    'speeding_analysis': SpeedingAnalysisPage(),
    'driving_behavior': DrivingBehaviorPage(),
    'statistics_report': StatisticsReportPage(),
    'advanced_analysis': AdvancedAnalysisPage()
}

def calculate_distance(lat1, lon1, lat2, lon2):
    """计算两点间距离（米）"""
    R = 6371000  # 地球半径（米）
    
    lat1_rad = math.radians(lat1)
    lat2_rad = math.radians(lat2)
    delta_lat = math.radians(lat2 - lat1)
    delta_lon = math.radians(lon2 - lon1)
    
    a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
         math.cos(lat1_rad) * math.cos(lat2_rad) * 
         math.sin(delta_lon/2) * math.sin(delta_lon/2))
    c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
    
    return R * c

def calculate_speed(df):
    """计算速度（km/h）"""
    speeds = []
    for i in range(len(df)):
        if i == 0:
            speeds.append(0)
        else:
            # 计算距离
            dist = calculate_distance(
                df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                df.iloc[i]['latitude'], df.iloc[i]['longitude']
            )
            
            # 计算时间差（秒）
            time_diff = (df.iloc[i]['timestamp'] - df.iloc[i-1]['timestamp']).total_seconds()
            
            if time_diff > 0:
                speed = (dist / time_diff) * 3.6  # 转换为km/h
                speeds.append(speed)
            else:
                speeds.append(0)
    
    return speeds

def detect_speeding_events(df, speed_limit=60):
    """检测超速事件"""
    speeding_events = []
    in_speeding = False
    start_idx = None
    
    for i, row in df.iterrows():
        if row['speed'] > speed_limit:
            if not in_speeding:
                in_speeding = True
                start_idx = i
        else:
            if in_speeding:
                in_speeding = False
                # 记录超速事件
                event_data = df.loc[start_idx:i-1]
                speeding_events.append({
                    'start_time': event_data.iloc[0]['timestamp'],
                    'end_time': event_data.iloc[-1]['timestamp'],
                    'duration': (event_data.iloc[-1]['timestamp'] - event_data.iloc[0]['timestamp']).total_seconds(),
                    'max_speed': event_data['speed'].max(),
                    'avg_speed': event_data['speed'].mean(),
                    'start_lat': event_data.iloc[0]['latitude'],
                    'start_lon': event_data.iloc[0]['longitude'],
                    'end_lat': event_data.iloc[-1]['latitude'],
                    'end_lon': event_data.iloc[-1]['longitude'],
                    'distance': event_data['distance'].sum() if 'distance' in event_data.columns else 0
                })
    
    return speeding_events

def analyze_driving_behavior(df):
    """分析驾驶行为"""
    behavior_metrics = {}
    
    if 'speed' in df.columns:
        # 速度相关指标
        behavior_metrics['avg_speed'] = df['speed'].mean()
        behavior_metrics['max_speed'] = df['speed'].max()
        behavior_metrics['speed_std'] = df['speed'].std()
        
        # 急加速检测（速度变化率 > 2 m/s²）
        speed_changes = df['speed'].diff() / 3.6  # 转换为m/s
        time_diffs = df['timestamp'].diff().dt.total_seconds()
        accelerations = speed_changes / time_diffs
        
        behavior_metrics['harsh_accelerations'] = len(accelerations[accelerations > 2])
        behavior_metrics['harsh_decelerations'] = len(accelerations[accelerations < -2])
        
        # 速度变化频率
        behavior_metrics['speed_changes'] = len(df[abs(df['speed'].diff()) > 5])
    
    # 行驶距离
    if 'distance' in df.columns:
        behavior_metrics['total_distance'] = df['distance'].sum()
    
    # 行驶时间
    if len(df) > 1:
        behavior_metrics['total_time'] = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
    
    return behavior_metrics

def render():
    """渲染车辆轨迹数据分析页面 - 面向对象版本"""

    st.markdown("""
    ### 🚗 车辆轨迹数据分析工具

    专业的车辆轨迹数据分析平台，支持超速分析、驾驶行为分析、轨迹可视化等功能。
    适用于大数据量的车辆GPS轨迹数据处理。

    **✨ 新版本特性：**
    - 面向对象架构，模块化设计
    - 更好的代码组织和维护性
    - 统一的数据管理和状态保持
    """)

    # 创建标签页
    tab1, tab2, tab3, tab4, tab5, tab6 = st.tabs([
        "📁 数据导入",
        "🗺️ 轨迹可视化",
        "⚡ 超速分析",
        "🚗 驾驶行为分析",
        "📊 统计报告",
        "⚙️ 高级分析"
    ])
    
    with tab1:
        # 使用数据导入页面类
        page_instances['data_import'].render()

    with tab2:
        # 使用轨迹可视化页面类
        page_instances['trajectory_visualization'].render()

    with tab3:
        # 使用超速分析页面类
        page_instances['speeding_analysis'].render()

    with tab4:
        # 使用驾驶行为分析页面类
        page_instances['driving_behavior'].render()

    with tab5:
        # 使用统计报告页面类
        page_instances['statistics_report'].render()

    with tab6:
        # 使用高级分析页面类
        page_instances['advanced_analysis'].render()
        st.markdown("""
        **支持的数据格式：**
        - CSV文件格式
        - 必需字段：`timestamp`, `latitude`, `longitude`
        - 可选字段：`speed`, `vehicle_id`, `driver_id`
        
        **示例数据格式：**
        ```
        timestamp,latitude,longitude,speed,vehicle_id
        2024-01-01 08:00:00,39.9042,116.4074,45.5,V001
        2024-01-01 08:00:30,39.9045,116.4078,52.3,V001
        ```
        
        **大数据处理：**
        - 支持百万级轨迹点数据
        - 自动数据采样和优化
        - 分批处理避免内存溢出
        """)
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择车辆轨迹CSV文件",
            type=['csv'],
            help="支持大文件上传，建议文件大小不超过500MB"
        )
        
        # 数据处理参数
        col1, col2 = st.columns(2)
        with col1:
            sample_rate = st.slider("数据采样率（%）", 1, 100, 100, 
                                  help="大数据量时可降低采样率提高处理速度")
        with col2:
            speed_limit = st.number_input("速度限制（km/h）", 20, 120, 60,
                                        help="用于超速分析的速度阈值")
        
        if uploaded_file is not None:
            try:
                # 显示文件信息
                file_size = uploaded_file.size / (1024 * 1024)  # MB
                st.info(f"📁 文件大小: {file_size:.2f} MB")
                
                with st.spinner("正在读取和处理数据..."):
                    # 读取CSV文件
                    df = pd.read_csv(uploaded_file)
                    df['timestamp'] = df['gps_time']
                    # 数据采样
                    if sample_rate < 100:
                        df = df.sample(frac=sample_rate/100).sort_index()
                        st.info(f"🔄 已应用 {sample_rate}% 采样率，当前数据量: {len(df):,} 条")
                    
                    # 数据预处理
                    if 'timestamp' in df.columns:
                        df['timestamp'] = pd.to_datetime(df['timestamp'])
                        df = df.sort_values('timestamp').reset_index(drop=True)
                    
                    # 计算速度（如果没有速度字段）
                    if 'speed' not in df.columns and len(df) > 1:
                        with st.spinner("正在计算速度..."):
                            df['speed'] = calculate_speed(df)
                    
                    # 计算距离
                    if len(df) > 1:
                        distances = []
                        for i in range(len(df)):
                            if i == 0:
                                distances.append(0)
                            else:
                                dist = calculate_distance(
                                    df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                                    df.iloc[i]['latitude'], df.iloc[i]['longitude']
                                )
                                distances.append(dist)
                        df['distance'] = distances
                    
                    # 保存到session state
                    st.session_state.trajectory_data = df
                    st.session_state.speed_limit = speed_limit
                
                st.success(f"✅ 数据处理完成！轨迹点数量: {len(df):,}")
                
                # 显示数据预览
                st.subheader("📋 数据预览")
                st.dataframe(df.head(10), use_container_width=True)
                
                # 数据基本信息
                col1, col2, col3 = st.columns(3)
                
                with col1:
                    st.metric("轨迹点数量", f"{len(df):,}")
                    if 'vehicle_id' in df.columns:
                        st.metric("车辆数量", df['vehicle_id'].nunique())
                
                with col2:
                    if 'timestamp' in df.columns and len(df) > 1:
                        duration = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
                        st.metric("时间跨度（小时）", f"{duration:.2f}")
                    
                    if 'speed' in df.columns:
                        st.metric("平均速度（km/h）", f"{df['speed'].mean():.1f}")
                
                with col3:
                    if 'distance' in df.columns:
                        total_distance = df['distance'].sum() / 1000  # 转换为公里
                        st.metric("总行驶距离（km）", f"{total_distance:.2f}")
                    
                    st.metric("内存使用（MB）", f"{df.memory_usage(deep=True).sum() / 1024**2:.2f}")
                
            except Exception as e:
                st.error(f"❌ 数据处理失败: {e}")
                st.info("请检查数据格式是否正确，确保包含必需的字段。")
        
        else:
            # 生成示例数据
            st.info("💡 您可以上传车辆轨迹CSV文件，或使用下面的示例数据进行体验")
            
            if st.button("🎲 生成示例轨迹数据", use_container_width=True):
                with st.spinner("正在生成示例数据..."):
                    # 生成模拟轨迹数据
                    np.random.seed(42)
                    n_points = 1000
                    
                    # 模拟北京市区轨迹
                    start_lat, start_lon = 39.9042, 116.4074
                    
                    timestamps = pd.date_range('2024-01-01 08:00:00', periods=n_points, freq='30S')
                    
                    # 生成轨迹点
                    lats, lons, speeds = [start_lat], [start_lon], [0]
                    
                    for i in range(1, n_points):
                        # 随机移动
                        lat_change = np.random.normal(0, 0.001)
                        lon_change = np.random.normal(0, 0.001)
                        
                        new_lat = lats[-1] + lat_change
                        new_lon = lons[-1] + lon_change
                        
                        lats.append(new_lat)
                        lons.append(new_lon)
                        
                        # 生成速度（包含一些超速情况）
                        if np.random.random() < 0.1:  # 10%概率超速
                            speed = np.random.uniform(65, 90)
                        else:
                            speed = np.random.uniform(20, 55)
                        speeds.append(speed)
                    
                    df = pd.DataFrame({
                        'timestamp': timestamps,
                        'latitude': lats,
                        'longitude': lons,
                        'speed': speeds,
                        'vehicle_id': ['V001'] * n_points
                    })
                    
                    # 计算距离
                    distances = []
                    for i in range(len(df)):
                        if i == 0:
                            distances.append(0)
                        else:
                            dist = calculate_distance(
                                df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                                df.iloc[i]['latitude'], df.iloc[i]['longitude']
                            )
                            distances.append(dist)
                    df['distance'] = distances
                    
                    st.session_state.trajectory_data = df
                    st.session_state.speed_limit = speed_limit
                
                st.success("✅ 示例数据生成成功！")
                st.rerun()

    with tab2:
        st.subheader("🗺️ 轨迹可视化")

        if 'trajectory_data' in st.session_state:
            df = st.session_state.trajectory_data

            # 可视化选项
            col1, col2, col3 = st.columns(3)

            with col1:
                show_speed_colors = st.checkbox("按速度着色", value=True)
            with col2:
                show_speeding_only = st.checkbox("仅显示超速点", value=False)
            with col3:
                max_points = st.selectbox("显示点数", [100, 500, 1000, 5000, "全部"], index=2)

            # 数据筛选
            plot_df = df.copy()

            if show_speeding_only and 'speed' in df.columns:
                speed_limit = st.session_state.get('speed_limit', 60)
                plot_df = plot_df[plot_df['speed'] > speed_limit]
                st.info(f"🚨 显示 {len(plot_df)} 个超速点（速度 > {speed_limit} km/h）")

            if max_points != "全部" and len(plot_df) > max_points:
                plot_df = plot_df.sample(n=max_points).sort_values('timestamp')
                st.info(f"📊 已采样显示 {max_points} 个点")

            if len(plot_df) > 0:
                # 创建轨迹图
                if show_speed_colors and 'speed' in plot_df.columns:
                    fig = px.scatter_mapbox(
                        plot_df,
                        lat="latitude",
                        lon="longitude",
                        color="speed",
                        color_continuous_scale="Viridis",
                        size_max=8,
                        zoom=12,
                        title="车辆轨迹（按速度着色）",
                        hover_data=['timestamp', 'speed'] if 'speed' in plot_df.columns else ['timestamp']
                    )
                else:
                    fig = px.scatter_mapbox(
                        plot_df,
                        lat="latitude",
                        lon="longitude",
                        size_max=8,
                        zoom=12,
                        title="车辆轨迹",
                        hover_data=['timestamp', 'speed'] if 'speed' in plot_df.columns else ['timestamp']
                    )

                fig.update_layout(
                    mapbox_style="open-street-map",
                    height=600,
                    margin={"r":0,"t":50,"l":0,"b":0}
                )

                st.plotly_chart(fig, use_container_width=True)

                # 轨迹统计
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    if 'distance' in df.columns:
                        total_distance = df['distance'].sum() / 1000
                        st.metric("总距离（km）", f"{total_distance:.2f}")

                with col2:
                    if 'speed' in df.columns:
                        avg_speed = df['speed'].mean()
                        st.metric("平均速度（km/h）", f"{avg_speed:.1f}")

                with col3:
                    if 'speed' in df.columns:
                        max_speed = df['speed'].max()
                        st.metric("最高速度（km/h）", f"{max_speed:.1f}")

                with col4:
                    if len(df) > 1:
                        duration = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
                        st.metric("行驶时间（小时）", f"{duration:.2f}")

            else:
                st.warning("⚠️ 没有符合条件的轨迹点数据")

        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或生成轨迹数据")

    with tab3:
        st.subheader("⚡ 超速分析")

        if 'trajectory_data' in st.session_state:
            df = st.session_state.trajectory_data
            speed_limit = st.session_state.get('speed_limit', 60)

            if 'speed' in df.columns:
                # 超速统计
                speeding_points = df[df['speed'] > speed_limit]
                speeding_ratio = len(speeding_points) / len(df) * 100

                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("超速点数", f"{len(speeding_points):,}")

                with col2:
                    st.metric("超速比例", f"{speeding_ratio:.1f}%")

                with col3:
                    if len(speeding_points) > 0:
                        max_speeding = speeding_points['speed'].max()
                        st.metric("最高超速（km/h）", f"{max_speeding:.1f}")

                with col4:
                    if len(speeding_points) > 0:
                        avg_speeding = speeding_points['speed'].mean()
                        st.metric("平均超速（km/h）", f"{avg_speeding:.1f}")

                # 超速事件检测
                st.subheader("🚨 超速事件分析")

                with st.spinner("正在分析超速事件..."):
                    speeding_events = detect_speeding_events(df, speed_limit)

                if speeding_events:
                    st.success(f"✅ 检测到 {len(speeding_events)} 个超速事件")

                    # 超速事件表格
                    events_df = pd.DataFrame(speeding_events)
                    events_df['duration_min'] = events_df['duration'] / 60
                    events_df['start_time'] = pd.to_datetime(events_df['start_time'])
                    events_df['end_time'] = pd.to_datetime(events_df['end_time'])

                    display_df = events_df[['start_time', 'duration_min', 'max_speed', 'avg_speed']].copy()
                    display_df.columns = ['开始时间', '持续时间(分钟)', '最高速度(km/h)', '平均速度(km/h)']
                    display_df = display_df.round(2)

                    st.dataframe(display_df, use_container_width=True)

                    # 超速事件分布图
                    col1, col2 = st.columns(2)

                    with col1:
                        # 超速持续时间分布
                        fig_duration = px.histogram(
                            events_df,
                            x='duration_min',
                            title='超速事件持续时间分布',
                            labels={'duration_min': '持续时间(分钟)', 'count': '事件数量'}
                        )
                        st.plotly_chart(fig_duration, use_container_width=True)

                    with col2:
                        # 超速严重程度分布
                        fig_severity = px.histogram(
                            events_df,
                            x='max_speed',
                            title='超速严重程度分布',
                            labels={'max_speed': '最高速度(km/h)', 'count': '事件数量'}
                        )
                        st.plotly_chart(fig_severity, use_container_width=True)

                else:
                    st.info("✅ 未检测到超速事件")

                # 速度分布分析
                st.subheader("📊 速度分布分析")

                col1, col2 = st.columns(2)

                with col1:
                    # 速度直方图
                    fig_speed_dist = px.histogram(
                        df,
                        x='speed',
                        title='速度分布直方图',
                        labels={'speed': '速度(km/h)', 'count': '频次'}
                    )
                    fig_speed_dist.add_vline(x=speed_limit, line_dash="dash", line_color="red",
                                           annotation_text=f"限速 {speed_limit} km/h")
                    st.plotly_chart(fig_speed_dist, use_container_width=True)

                with col2:
                    # 速度时间序列
                    if 'timestamp' in df.columns:
                        fig_speed_time = px.line(
                            df.sample(min(1000, len(df))),  # 采样显示
                            x='timestamp',
                            y='speed',
                            title='速度时间序列'
                        )
                        fig_speed_time.add_hline(y=speed_limit, line_dash="dash", line_color="red",
                                               annotation_text=f"限速 {speed_limit} km/h")
                        st.plotly_chart(fig_speed_time, use_container_width=True)

            else:
                st.warning("⚠️ 数据中缺少速度信息，无法进行超速分析")

        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或生成轨迹数据")

    with tab4:
        st.subheader("🚗 驾驶行为分析")

        if 'trajectory_data' in st.session_state:
            df = st.session_state.trajectory_data

            # 计算驾驶行为指标
            with st.spinner("正在分析驾驶行为..."):
                behavior_metrics = analyze_driving_behavior(df)

            # 显示关键指标
            st.subheader("📊 驾驶行为指标")

            col1, col2, col3, col4 = st.columns(4)

            with col1:
                if 'avg_speed' in behavior_metrics:
                    st.metric("平均速度（km/h）", f"{behavior_metrics['avg_speed']:.1f}")
                if 'total_distance' in behavior_metrics:
                    distance_km = behavior_metrics['total_distance'] / 1000
                    st.metric("总行驶距离（km）", f"{distance_km:.2f}")

            with col2:
                if 'max_speed' in behavior_metrics:
                    st.metric("最高速度（km/h）", f"{behavior_metrics['max_speed']:.1f}")
                if 'total_time' in behavior_metrics:
                    st.metric("总行驶时间（小时）", f"{behavior_metrics['total_time']:.2f}")

            with col3:
                if 'harsh_accelerations' in behavior_metrics:
                    st.metric("急加速次数", behavior_metrics['harsh_accelerations'])
                if 'speed_std' in behavior_metrics:
                    st.metric("速度标准差", f"{behavior_metrics['speed_std']:.1f}")

            with col4:
                if 'harsh_decelerations' in behavior_metrics:
                    st.metric("急减速次数", behavior_metrics['harsh_decelerations'])
                if 'speed_changes' in behavior_metrics:
                    st.metric("频繁变速次数", behavior_metrics['speed_changes'])

            # 驾驶行为评分
            st.subheader("🏆 驾驶行为评分")

            # 计算综合评分（0-100分）
            score = 100

            # 超速扣分
            if 'speed' in df.columns:
                speed_limit = st.session_state.get('speed_limit', 60)
                speeding_ratio = len(df[df['speed'] > speed_limit]) / len(df)
                score -= speeding_ratio * 30  # 超速扣分

            # 急加速/急减速扣分
            if 'harsh_accelerations' in behavior_metrics and 'total_time' in behavior_metrics:
                harsh_rate = (behavior_metrics['harsh_accelerations'] + behavior_metrics['harsh_decelerations']) / behavior_metrics['total_time']
                score -= min(harsh_rate * 10, 20)  # 急操作扣分

            # 速度变化扣分
            if 'speed_changes' in behavior_metrics and 'total_time' in behavior_metrics:
                change_rate = behavior_metrics['speed_changes'] / behavior_metrics['total_time']
                score -= min(change_rate * 5, 15)  # 频繁变速扣分

            score = max(0, min(100, score))  # 确保分数在0-100之间

            # 评分显示
            col1, col2 = st.columns([1, 2])

            with col1:
                # 评分仪表盘
                fig_score = go.Figure(go.Indicator(
                    mode = "gauge+number+delta",
                    value = score,
                    domain = {'x': [0, 1], 'y': [0, 1]},
                    title = {'text': "驾驶行为评分"},
                    delta = {'reference': 80},
                    gauge = {
                        'axis': {'range': [None, 100]},
                        'bar': {'color': "darkblue"},
                        'steps': [
                            {'range': [0, 50], 'color': "lightgray"},
                            {'range': [50, 80], 'color': "yellow"},
                            {'range': [80, 100], 'color': "green"}
                        ],
                        'threshold': {
                            'line': {'color': "red", 'width': 4},
                            'thickness': 0.75,
                            'value': 90
                        }
                    }
                ))
                fig_score.update_layout(height=300)
                st.plotly_chart(fig_score, use_container_width=True)

            with col2:
                # 评分说明
                if score >= 90:
                    st.success("🌟 优秀驾驶员")
                    st.write("驾驶行为非常规范，安全意识强")
                elif score >= 80:
                    st.info("👍 良好驾驶员")
                    st.write("驾驶行为较为规范，有小幅改进空间")
                elif score >= 60:
                    st.warning("⚠️ 一般驾驶员")
                    st.write("驾驶行为需要改进，注意安全驾驶")
                else:
                    st.error("🚨 危险驾驶员")
                    st.write("驾驶行为存在严重问题，需要立即改进")

                # 改进建议
                st.subheader("💡 改进建议")
                suggestions = []

                if 'speed' in df.columns:
                    speed_limit = st.session_state.get('speed_limit', 60)
                    speeding_ratio = len(df[df['speed'] > speed_limit]) / len(df)
                    if speeding_ratio > 0.1:
                        suggestions.append("• 减少超速行为，遵守交通限速规定")

                if 'harsh_accelerations' in behavior_metrics and behavior_metrics['harsh_accelerations'] > 5:
                    suggestions.append("• 避免急加速，平稳起步")

                if 'harsh_decelerations' in behavior_metrics and behavior_metrics['harsh_decelerations'] > 5:
                    suggestions.append("• 避免急刹车，提前减速")

                if 'speed_changes' in behavior_metrics and behavior_metrics['speed_changes'] > 20:
                    suggestions.append("• 保持稳定车速，减少频繁变速")

                if not suggestions:
                    suggestions.append("• 继续保持良好的驾驶习惯")

                for suggestion in suggestions:
                    st.write(suggestion)

            # 行为趋势分析
            if 'speed' in df.columns and 'timestamp' in df.columns:
                st.subheader("📈 驾驶行为趋势")

                # 按小时统计驾驶行为
                df_hourly = df.copy()
                df_hourly['hour'] = df_hourly['timestamp'].dt.hour

                hourly_stats = df_hourly.groupby('hour').agg({
                    'speed': ['mean', 'max', 'std']
                }).round(2)

                hourly_stats.columns = ['平均速度', '最高速度', '速度标准差']

                # 绘制小时趋势图
                fig_hourly = make_subplots(
                    rows=2, cols=2,
                    subplot_titles=('平均速度趋势', '最高速度趋势', '速度变化趋势', '小时统计表'),
                    specs=[[{"secondary_y": False}, {"secondary_y": False}],
                           [{"secondary_y": False}, {"type": "table"}]]
                )

                # 平均速度
                fig_hourly.add_trace(
                    go.Scatter(x=hourly_stats.index, y=hourly_stats['平均速度'],
                             mode='lines+markers', name='平均速度'),
                    row=1, col=1
                )

                # 最高速度
                fig_hourly.add_trace(
                    go.Scatter(x=hourly_stats.index, y=hourly_stats['最高速度'],
                             mode='lines+markers', name='最高速度', line=dict(color='red')),
                    row=1, col=2
                )

                # 速度标准差
                fig_hourly.add_trace(
                    go.Scatter(x=hourly_stats.index, y=hourly_stats['速度标准差'],
                             mode='lines+markers', name='速度标准差', line=dict(color='orange')),
                    row=2, col=1
                )

                # 统计表
                fig_hourly.add_trace(
                    go.Table(
                        header=dict(values=['小时', '平均速度', '最高速度', '速度标准差']),
                        cells=dict(values=[hourly_stats.index,
                                         hourly_stats['平均速度'],
                                         hourly_stats['最高速度'],
                                         hourly_stats['速度标准差']])
                    ),
                    row=2, col=2
                )

                fig_hourly.update_layout(height=600, showlegend=False)
                st.plotly_chart(fig_hourly, use_container_width=True)

        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或生成轨迹数据")

    with tab5:
        st.subheader("📊 统计报告")

        if 'trajectory_data' in st.session_state:
            df = st.session_state.trajectory_data

            # 生成综合报告
            st.subheader("📋 综合分析报告")

            # 基础统计
            col1, col2 = st.columns(2)

            with col1:
                st.markdown("**📈 基础统计信息**")

                report_data = {
                    "轨迹点总数": f"{len(df):,}",
                    "数据时间跨度": "",
                    "总行驶距离": "",
                    "平均速度": "",
                    "最高速度": "",
                    "数据完整性": f"{(1 - df.isnull().sum().sum() / (len(df) * len(df.columns))) * 100:.1f}%"
                }

                if 'timestamp' in df.columns and len(df) > 1:
                    duration = df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]
                    report_data["数据时间跨度"] = f"{duration.total_seconds() / 3600:.2f} 小时"

                if 'distance' in df.columns:
                    total_distance = df['distance'].sum() / 1000
                    report_data["总行驶距离"] = f"{total_distance:.2f} km"

                if 'speed' in df.columns:
                    report_data["平均速度"] = f"{df['speed'].mean():.1f} km/h"
                    report_data["最高速度"] = f"{df['speed'].max():.1f} km/h"

                for key, value in report_data.items():
                    st.write(f"• **{key}**: {value}")

            with col2:
                st.markdown("**🚨 安全分析**")

                safety_data = {}

                if 'speed' in df.columns:
                    speed_limit = st.session_state.get('speed_limit', 60)
                    speeding_points = df[df['speed'] > speed_limit]
                    speeding_events = detect_speeding_events(df, speed_limit)

                    safety_data.update({
                        "超速点数量": f"{len(speeding_points):,}",
                        "超速比例": f"{len(speeding_points) / len(df) * 100:.1f}%",
                        "超速事件数": f"{len(speeding_events)}",
                        "最严重超速": f"{speeding_points['speed'].max():.1f} km/h" if len(speeding_points) > 0 else "无"
                    })

                    # 计算驾驶行为指标
                    behavior_metrics = analyze_driving_behavior(df)
                    if 'harsh_accelerations' in behavior_metrics:
                        safety_data["急加速次数"] = f"{behavior_metrics['harsh_accelerations']}"
                    if 'harsh_decelerations' in behavior_metrics:
                        safety_data["急减速次数"] = f"{behavior_metrics['harsh_decelerations']}"

                for key, value in safety_data.items():
                    st.write(f"• **{key}**: {value}")

            # 数据质量评估
            st.subheader("🔍 数据质量评估")

            quality_metrics = {}

            # 检查数据完整性
            missing_data = df.isnull().sum()
            quality_metrics["数据完整性"] = f"{(1 - missing_data.sum() / (len(df) * len(df.columns))) * 100:.1f}%"

            # 检查时间连续性
            if 'timestamp' in df.columns and len(df) > 1:
                time_diffs = df['timestamp'].diff().dt.total_seconds()
                avg_interval = time_diffs.mean()
                max_gap = time_diffs.max()
                quality_metrics["平均采样间隔"] = f"{avg_interval:.1f} 秒"
                quality_metrics["最大时间间隔"] = f"{max_gap:.1f} 秒"

            # 检查位置合理性
            if 'latitude' in df.columns and 'longitude' in df.columns:
                lat_range = df['latitude'].max() - df['latitude'].min()
                lon_range = df['longitude'].max() - df['longitude'].min()
                quality_metrics["纬度范围"] = f"{lat_range:.4f}°"
                quality_metrics["经度范围"] = f"{lon_range:.4f}°"

            # 检查速度合理性
            if 'speed' in df.columns:
                unrealistic_speeds = len(df[df['speed'] > 200])  # 超过200km/h认为不合理
                quality_metrics["异常速度点"] = f"{unrealistic_speeds}"

            col1, col2 = st.columns(2)
            with col1:
                for i, (key, value) in enumerate(quality_metrics.items()):
                    if i < len(quality_metrics) // 2:
                        st.write(f"• **{key}**: {value}")

            with col2:
                for i, (key, value) in enumerate(quality_metrics.items()):
                    if i >= len(quality_metrics) // 2:
                        st.write(f"• **{key}**: {value}")

            # 导出报告
            st.subheader("📤 导出报告")

            col1, col2, col3 = st.columns(3)

            with col1:
                if st.button("📊 生成Excel报告"):
                    # 这里可以生成Excel报告
                    st.success("✅ Excel报告生成功能开发中...")

            with col2:
                if st.button("📄 生成PDF报告"):
                    # 这里可以生成PDF报告
                    st.success("✅ PDF报告生成功能开发中...")

            with col3:
                # 导出原始数据
                csv_data = df.to_csv(index=False)
                st.download_button(
                    "📥 下载处理后数据",
                    csv_data,
                    "processed_trajectory_data.csv",
                    "text/csv"
                )

        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或生成轨迹数据")

    with tab6:
        st.subheader("⚙️ 高级分析")

        if 'trajectory_data' in st.session_state:
            df = st.session_state.trajectory_data

            # 高级分析选项
            analysis_type = st.selectbox(
                "选择分析类型",
                ["轨迹聚类分析", "异常检测", "路径优化分析", "驾驶模式识别", "燃油消耗估算"]
            )

            if analysis_type == "轨迹聚类分析":
                st.subheader("🎯 轨迹聚类分析")

                if 'latitude' in df.columns and 'longitude' in df.columns:
                    # 聚类参数
                    col1, col2 = st.columns(2)
                    with col1:
                        n_clusters = st.slider("聚类数量", 2, 10, 5)
                    with col2:
                        sample_size = st.slider("采样点数", 100, min(1000, len(df)), 500)

                    if st.button("🔍 执行聚类分析"):
                        with st.spinner("正在进行聚类分析..."):
                            # 采样数据
                            sample_df = df.sample(n=sample_size)

                            # 使用简化的聚类方法（避免sklearn依赖）
                            try:
                                from sklearn.cluster import KMeans
                                coords = sample_df[['latitude', 'longitude']].values
                                kmeans = KMeans(n_clusters=n_clusters, random_state=42)
                                clusters = kmeans.fit_predict(coords)
                            except ImportError:
                                # 如果没有sklearn，使用简单的随机分组
                                st.warning("⚠️ 未安装sklearn，使用简化聚类方法")
                                clusters = np.random.randint(0, n_clusters, len(sample_df))

                            sample_df = sample_df.copy()
                            sample_df['cluster'] = clusters

                            # 可视化聚类结果
                            fig = px.scatter_mapbox(
                                sample_df,
                                lat="latitude",
                                lon="longitude",
                                color="cluster",
                                color_continuous_scale="Viridis",
                                zoom=12,
                                title="轨迹聚类结果"
                            )
                            fig.update_layout(mapbox_style="open-street-map", height=500)
                            st.plotly_chart(fig, use_container_width=True)

                            # 聚类统计
                            # 聚类统计
                            agg_dict = {
                                'latitude': ['count', 'mean'],
                                'longitude': 'mean'
                            }
                            if 'speed' in sample_df.columns:
                                agg_dict['speed'] = 'mean'

                            cluster_stats = sample_df.groupby('cluster').agg(agg_dict).round(4)

                            st.subheader("📊 聚类统计")
                            st.dataframe(cluster_stats, use_container_width=True)

                else:
                    st.warning("⚠️ 缺少位置信息，无法进行聚类分析")

            elif analysis_type == "异常检测":
                st.subheader("🔍 异常检测")

                if 'speed' in df.columns:
                    # 异常检测参数
                    col1, col2 = st.columns(2)
                    with col1:
                        speed_threshold = st.number_input("速度异常阈值（km/h）", 100, 300, 150)
                    with col2:
                        z_score_threshold = st.number_input("Z-score阈值", 1.0, 5.0, 3.0)

                    if st.button("🔍 检测异常"):
                        with st.spinner("正在检测异常..."):
                            # 速度异常
                            speed_anomalies = df[df['speed'] > speed_threshold]

                            # Z-score异常检测（使用numpy实现）
                            try:
                                from scipy import stats
                                z_scores = np.abs(stats.zscore(df['speed']))
                            except ImportError:
                                # 如果没有scipy，手动计算z-score
                                mean_speed = df['speed'].mean()
                                std_speed = df['speed'].std()
                                z_scores = np.abs((df['speed'] - mean_speed) / std_speed)

                            z_anomalies = df[z_scores > z_score_threshold]

                            col1, col2 = st.columns(2)

                            with col1:
                                st.metric("速度异常点", len(speed_anomalies))
                                if len(speed_anomalies) > 0:
                                    st.dataframe(speed_anomalies[['timestamp', 'speed', 'latitude', 'longitude']].head())

                            with col2:
                                st.metric("统计异常点", len(z_anomalies))
                                if len(z_anomalies) > 0:
                                    st.dataframe(z_anomalies[['timestamp', 'speed', 'latitude', 'longitude']].head())

                else:
                    st.warning("⚠️ 缺少速度信息，无法进行异常检测")

            elif analysis_type == "燃油消耗估算":
                st.subheader("⛽ 燃油消耗估算")

                if 'speed' in df.columns and 'distance' in df.columns:
                    # 燃油消耗参数
                    col1, col2 = st.columns(2)
                    with col1:
                        vehicle_type = st.selectbox("车辆类型", ["小型车", "中型车", "大型车", "货车"])
                        # 根据车辆类型调整基础油耗
                        vehicle_consumption = {
                            "小型车": 6.0,
                            "中型车": 8.0,
                            "大型车": 12.0,
                            "货车": 15.0
                        }
                        default_consumption = vehicle_consumption.get(vehicle_type, 8.0)
                    with col2:
                        base_consumption = st.number_input("基础油耗（L/100km）", 5.0, 20.0, default_consumption)

                    if st.button("⛽ 计算燃油消耗"):
                        with st.spinner("正在计算燃油消耗..."):
                            # 简化的燃油消耗模型
                            total_distance_km = df['distance'].sum() / 1000

                            # 根据速度调整油耗
                            speed_factor = 1.0
                            avg_speed = df['speed'].mean()

                            if avg_speed > 80:
                                speed_factor = 1.2  # 高速增加油耗
                            elif avg_speed < 30:
                                speed_factor = 1.3  # 低速增加油耗

                            estimated_consumption = total_distance_km * base_consumption * speed_factor / 100

                            col1, col2, col3 = st.columns(3)

                            with col1:
                                st.metric("总行驶距离（km）", f"{total_distance_km:.2f}")

                            with col2:
                                st.metric("估算油耗（L）", f"{estimated_consumption:.2f}")

                            with col3:
                                fuel_cost = estimated_consumption * 7.5  # 假设油价7.5元/L
                                st.metric("估算油费（元）", f"{fuel_cost:.2f}")

                            # 油耗分析图
                            hourly_consumption = []
                            if 'timestamp' in df.columns:
                                df_hourly = df.copy()
                                df_hourly['hour'] = df_hourly['timestamp'].dt.hour

                                for hour in range(24):
                                    hour_data = df_hourly[df_hourly['hour'] == hour]
                                    if len(hour_data) > 0:
                                        hour_distance = hour_data['distance'].sum() / 1000
                                        hour_avg_speed = hour_data['speed'].mean()

                                        hour_speed_factor = 1.0
                                        if hour_avg_speed > 80:
                                            hour_speed_factor = 1.2
                                        elif hour_avg_speed < 30:
                                            hour_speed_factor = 1.3

                                        hour_consumption = hour_distance * base_consumption * hour_speed_factor / 100
                                        hourly_consumption.append(hour_consumption)
                                    else:
                                        hourly_consumption.append(0)

                                fig_consumption = px.bar(
                                    x=list(range(24)),
                                    y=hourly_consumption,
                                    title="每小时燃油消耗估算",
                                    labels={'x': '小时', 'y': '燃油消耗（L）'}
                                )
                                st.plotly_chart(fig_consumption, use_container_width=True)

                else:
                    st.warning("⚠️ 缺少速度或距离信息，无法进行燃油消耗估算")

            else:
                st.info(f"🚧 {analysis_type} 功能正在开发中...")

        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或生成轨迹数据")
