# Panel应用程序模板

这是一个使用Panel组件构建的应用程序模板，包含左侧工具栏和右侧页面切换功能。

## 功能特性

- 🎯 **左侧工具栏**: 分组显示的导航按钮
- 📱 **响应式布局**: 自适应不同屏幕尺寸
- 🔄 **页面切换**: 点击按钮切换不同功能页面
- 🎨 **美观界面**: 现代化的UI设计
- 📊 **丰富组件**: 包含图表、表格、表单等多种组件

## 页面结构

### 主要功能
- **📊 仪表板**: 显示关键指标和概览信息
- **📈 数据分析**: 数据导入和分析工具
- **📉 图表**: 各种图表展示功能

### 管理功能
- **📋 报告**: 报告生成和下载
- **🔧 工具**: 实用工具集合
- **⚙️ 设置**: 系统配置和用户设置

## 快速开始

### 1. 安装依赖

确保已安装Panel：
```bash
pip install panel>=1.7.2
```

或者如果使用uv：
```bash
uv add panel>=1.7.2
```

### 2. 运行应用程序

```bash
python run_app.py
```

或者直接运行主文件：
```bash
python panel_app_template.py
```

### 3. 访问应用程序

打开浏览器访问: http://localhost:5007

## 自定义和扩展

### 添加新页面

1. 在`AppTemplate`类中添加新的页面创建方法：
```python
def create_new_page(self):
    return pn.Column(
        pn.pane.Markdown("# 新页面"),
        # 添加页面内容
        sizing_mode="stretch_width"
    )
```

2. 在`setup_pages`方法中注册新页面：
```python
def setup_pages(self):
    self.pages = {
        # 现有页面...
        "new_page": self.create_new_page(),
    }
```

3. 在`setup_toolbar`方法中添加对应的按钮：
```python
toolbar_groups = {
    "主要功能": [
        # 现有按钮...
        ("🆕 新页面", "new_page"),
    ],
    # 其他组...
}
```

### 修改样式

可以通过以下方式自定义样式：

1. **修改工具栏宽度**:
```python
self.toolbar = pn.Column(
    # ...
    width=250,  # 修改宽度
    # ...
)
```

2. **修改按钮样式**:
```python
btn = pn.widgets.Button(
    name=button_text,
    button_type="outline",  # 可选: "primary", "outline", "light"
    width=200,  # 修改按钮宽度
    # ...
)
```

3. **修改整体高度**:
```python
self.layout = pn.Row(
    # ...
    height=900,  # 修改应用程序高度
)
```

### 添加交互功能

可以为按钮和组件添加回调函数：

```python
def on_button_click(self, event):
    """按钮点击事件处理"""
    print(f"按钮被点击: {event.obj.name}")

# 绑定事件
button.param.watch(self.on_button_click, 'clicks')
```

## 项目结构

```
.
├── panel_app_template.py    # 主应用程序文件
├── run_app.py              # 启动脚本
├── PANEL_APP_README.md     # 说明文档
└── pyproject.toml          # 项目配置
```

## 技术栈

- **Panel**: Python数据应用程序框架
- **Bokeh**: 交互式可视化库
- **Param**: 参数化编程库

## 注意事项

1. 确保Python版本 >= 3.11
2. 首次运行可能需要下载一些JavaScript依赖
3. 如果端口5007被占用，可以修改`run_app.py`中的端口号
4. 开发时建议启用`autoreload=True`以便实时查看更改

## 扩展建议

- 添加用户认证功能
- 集成数据库连接
- 添加更多图表类型
- 实现数据导出功能
- 添加主题切换功能
- 集成API接口

## 故障排除

如果遇到问题，请检查：

1. Panel版本是否正确安装
2. 端口是否被其他程序占用
3. 浏览器是否支持现代JavaScript特性
4. 防火墙是否阻止了本地连接

更多帮助请参考Panel官方文档: https://panel.holoviz.org/
