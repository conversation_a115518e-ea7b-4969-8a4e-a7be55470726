import streamlit as st
import pandas as pd
import numpy as np
import json
from datetime import datetime

def render():
    """渲染实用工具页面"""
    
    st.markdown("""
    ### 🔧 实用工具集合
    
    这里提供了各种实用的工具，帮助您提高工作效率。
    """)
    
    # 创建工具网格
    col1, col2 = st.columns(2)
    
    with col1:
        # 数据转换工具
        with st.expander("🔄 数据转换工具", expanded=True):
            st.subheader("CSV ↔ JSON 转换")
            
            conversion_type = st.radio(
                "转换类型",
                ["CSV to JSON", "JSON to CSV"],
                horizontal=True
            )
            
            if conversion_type == "CSV to JSON":
                csv_input = st.text_area(
                    "输入CSV数据",
                    placeholder="name,age,city\nJohn,25,New York\nJane,30,London",
                    height=100
                )
                
                if csv_input and st.button("转换为JSON"):
                    try:
                        from io import StringIO
                        df = pd.read_csv(StringIO(csv_input))
                        json_output = df.to_json(orient='records', indent=2)
                        st.code(json_output, language='json')
                        
                        st.download_button(
                            "📥 下载JSON文件",
                            json_output,
                            "data.json",
                            "application/json"
                        )
                    except Exception as e:
                        st.error(f"转换失败: {e}")
            
            else:  # JSON to CSV
                json_input = st.text_area(
                    "输入JSON数据",
                    placeholder='[{"name":"John","age":25,"city":"New York"}]',
                    height=100
                )
                
                if json_input and st.button("转换为CSV"):
                    try:
                        data = json.loads(json_input)
                        df = pd.DataFrame(data)
                        csv_output = df.to_csv(index=False)
                        st.code(csv_output, language='text')
                        
                        st.download_button(
                            "📥 下载CSV文件",
                            csv_output,
                            "data.csv",
                            "text/csv"
                        )
                    except Exception as e:
                        st.error(f"转换失败: {e}")
        
        # 文本处理工具
        with st.expander("📝 文本处理工具"):
            st.subheader("文本统计与处理")
            
            text_input = st.text_area(
                "输入文本",
                placeholder="在这里输入您要处理的文本...",
                height=150
            )
            
            if text_input:
                # 文本统计
                word_count = len(text_input.split())
                char_count = len(text_input)
                char_count_no_spaces = len(text_input.replace(' ', ''))
                line_count = len(text_input.split('\n'))
                
                col_a, col_b, col_c, col_d = st.columns(4)
                with col_a:
                    st.metric("字数", word_count)
                with col_b:
                    st.metric("字符数", char_count)
                with col_c:
                    st.metric("字符数(无空格)", char_count_no_spaces)
                with col_d:
                    st.metric("行数", line_count)
                
                # 文本处理选项
                st.subheader("文本处理")
                
                col_x, col_y = st.columns(2)
                
                with col_x:
                    if st.button("转换为大写"):
                        st.code(text_input.upper())
                    
                    if st.button("转换为小写"):
                        st.code(text_input.lower())
                
                with col_y:
                    if st.button("移除空行"):
                        cleaned_text = '\n'.join([line for line in text_input.split('\n') if line.strip()])
                        st.code(cleaned_text)
                    
                    if st.button("移除多余空格"):
                        cleaned_text = ' '.join(text_input.split())
                        st.code(cleaned_text)
    
    with col2:
        # 时间工具
        with st.expander("⏰ 时间工具", expanded=True):
            st.subheader("时间戳转换")
            
            # 当前时间
            current_time = datetime.now()
            current_timestamp = int(current_time.timestamp())
            
            st.write(f"**当前时间:** {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            st.write(f"**当前时间戳:** {current_timestamp}")
            
            # 时间戳转换
            timestamp_input = st.number_input(
                "输入时间戳",
                value=current_timestamp,
                help="输入10位或13位时间戳"
            )
            
            if st.button("转换时间戳"):
                try:
                    # 处理13位时间戳（毫秒）
                    if len(str(int(timestamp_input))) == 13:
                        timestamp_input = timestamp_input / 1000
                    
                    converted_time = datetime.fromtimestamp(timestamp_input)
                    st.success(f"转换结果: {converted_time.strftime('%Y-%m-%d %H:%M:%S')}")
                except Exception as e:
                    st.error(f"转换失败: {e}")
            
            # 日期转时间戳
            st.subheader("日期转时间戳")
            
            date_input = st.date_input("选择日期")
            time_input = st.time_input("选择时间")
            
            if st.button("转换为时间戳"):
                selected_datetime = datetime.combine(date_input, time_input)
                timestamp = int(selected_datetime.timestamp())
                st.success(f"时间戳: {timestamp}")
        
        # 编码解码工具
        with st.expander("🔐 编码解码工具"):
            st.subheader("Base64 编码/解码")
            
            encode_decode = st.radio(
                "操作类型",
                ["编码", "解码"],
                horizontal=True
            )
            
            if encode_decode == "编码":
                text_to_encode = st.text_input("输入要编码的文本")
                
                if text_to_encode and st.button("Base64 编码"):
                    import base64
                    encoded = base64.b64encode(text_to_encode.encode()).decode()
                    st.code(encoded)
                    
                    st.download_button(
                        "📥 下载编码结果",
                        encoded,
                        "encoded.txt",
                        "text/plain"
                    )
            
            else:  # 解码
                text_to_decode = st.text_input("输入要解码的Base64文本")
                
                if text_to_decode and st.button("Base64 解码"):
                    try:
                        import base64
                        decoded = base64.b64decode(text_to_decode).decode()
                        st.code(decoded)
                    except Exception as e:
                        st.error(f"解码失败: {e}")
    
    # 第二行工具
    st.markdown("---")
    
    col1, col2 = st.columns(2)
    
    with col1:
        # 随机数生成器
        with st.expander("🎲 随机数生成器"):
            st.subheader("生成随机数")
            
            random_type = st.selectbox(
                "随机数类型",
                ["整数", "小数", "UUID", "密码"]
            )
            
            if random_type == "整数":
                min_val = st.number_input("最小值", value=1)
                max_val = st.number_input("最大值", value=100)
                count = st.number_input("生成数量", value=10, min_value=1, max_value=1000)
                
                if st.button("生成随机整数"):
                    random_numbers = np.random.randint(min_val, max_val + 1, count)
                    st.write("生成的随机数:")
                    st.code(', '.join(map(str, random_numbers)))
            
            elif random_type == "小数":
                min_val = st.number_input("最小值", value=0.0)
                max_val = st.number_input("最大值", value=1.0)
                count = st.number_input("生成数量", value=10, min_value=1, max_value=1000)
                precision = st.number_input("小数位数", value=2, min_value=1, max_value=10)
                
                if st.button("生成随机小数"):
                    random_numbers = np.random.uniform(min_val, max_val, count)
                    formatted_numbers = [f"{num:.{precision}f}" for num in random_numbers]
                    st.write("生成的随机数:")
                    st.code(', '.join(formatted_numbers))
            
            elif random_type == "UUID":
                count = st.number_input("生成数量", value=5, min_value=1, max_value=100)
                
                if st.button("生成UUID"):
                    import uuid
                    uuids = [str(uuid.uuid4()) for _ in range(count)]
                    st.write("生成的UUID:")
                    for u in uuids:
                        st.code(u)
            
            elif random_type == "密码":
                length = st.number_input("密码长度", value=12, min_value=4, max_value=50)
                include_symbols = st.checkbox("包含特殊字符", value=True)
                count = st.number_input("生成数量", value=5, min_value=1, max_value=20)
                
                if st.button("生成随机密码"):
                    import string
                    import random
                    
                    chars = string.ascii_letters + string.digits
                    if include_symbols:
                        chars += "!@#$%^&*"
                    
                    passwords = []
                    for _ in range(count):
                        password = ''.join(random.choice(chars) for _ in range(length))
                        passwords.append(password)
                    
                    st.write("生成的密码:")
                    for pwd in passwords:
                        st.code(pwd)
    
    with col2:
        # 颜色工具
        with st.expander("🎨 颜色工具"):
            st.subheader("颜色转换")
            
            # 颜色选择器
            selected_color = st.color_picker("选择颜色", "#FF6B6B")
            
            # 显示颜色信息
            def hex_to_rgb(hex_color):
                hex_color = hex_color.lstrip('#')
                return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
            
            def rgb_to_hsl(r, g, b):
                r, g, b = r/255.0, g/255.0, b/255.0
                max_val = max(r, g, b)
                min_val = min(r, g, b)
                h, s, l = 0, 0, (max_val + min_val) / 2
                
                if max_val == min_val:
                    h = s = 0
                else:
                    d = max_val - min_val
                    s = d / (2 - max_val - min_val) if l > 0.5 else d / (max_val + min_val)
                    if max_val == r:
                        h = (g - b) / d + (6 if g < b else 0)
                    elif max_val == g:
                        h = (b - r) / d + 2
                    elif max_val == b:
                        h = (r - g) / d + 4
                    h /= 6
                
                return int(h*360), int(s*100), int(l*100)
            
            rgb = hex_to_rgb(selected_color)
            hsl = rgb_to_hsl(*rgb)
            
            st.write("**颜色信息:**")
            st.write(f"HEX: {selected_color}")
            st.write(f"RGB: rgb({rgb[0]}, {rgb[1]}, {rgb[2]})")
            st.write(f"HSL: hsl({hsl[0]}, {hsl[1]}%, {hsl[2]}%)")
            
            # 生成调色板
            if st.button("生成调色板"):
                st.write("**相似色调色板:**")
                
                base_hue = hsl[0]
                colors = []
                
                for i in range(5):
                    hue_shift = (base_hue + i * 30) % 360
                    colors.append(f"hsl({hue_shift}, {hsl[1]}%, {hsl[2]}%)")
                
                cols = st.columns(5)
                for i, color in enumerate(colors):
                    with cols[i]:
                        st.markdown(
                            f'<div style="background-color: {color}; height: 50px; border-radius: 5px; margin: 5px 0;"></div>',
                            unsafe_allow_html=True
                        )
                        st.caption(color)
