#!/usr/bin/env python3
"""
测试字段映射功能的脚本
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta

def create_test_data():
    """创建不同字段名格式的测试数据"""
    
    # 测试数据集1：标准字段名
    print("创建测试数据集...")
    
    # 生成时间序列
    start_time = datetime(2024, 1, 1, 8, 0, 0)
    timestamps = [start_time + timedelta(seconds=30*i) for i in range(100)]
    
    # 生成轨迹坐标（北京市区）
    base_lat, base_lon = 39.9042, 116.4074
    lats = [base_lat + np.random.normal(0, 0.001) for _ in range(100)]
    lons = [base_lon + np.random.normal(0, 0.001) for _ in range(100)]
    
    # 生成速度数据
    speeds = [np.random.uniform(20, 80) for _ in range(100)]
    
    # 车辆和驾驶员信息
    vehicle_ids = ['V001'] * 100
    driver_ids = ['张三'] * 100
    
    # 测试数据集1：非标准字段名
    test_data_1 = pd.DataFrame({
        'datetime': timestamps,
        'lat': lats,
        'lng': lons,
        'velocity': speeds,
        'car_id': vehicle_ids,
        'driver_name': driver_ids
    })
    
    # 测试数据集2：另一种非标准字段名
    test_data_2 = pd.DataFrame({
        'time': timestamps,
        'latitude': lats,
        'longitude': lons,
        'speed': speeds,
        'vehicle': vehicle_ids,
        'driver': driver_ids
    })
    
    # 测试数据集3：中文字段名
    test_data_3 = pd.DataFrame({
        '时间': timestamps,
        '纬度': lats,
        '经度': lons,
        '速度': speeds,
        '车辆ID': vehicle_ids,
        '驾驶员': driver_ids
    })
    
    # 保存测试数据
    test_data_1.to_csv('test_data_nonstandard_1.csv', index=False)
    test_data_2.to_csv('test_data_nonstandard_2.csv', index=False)
    test_data_3.to_csv('test_data_chinese.csv', index=False)
    
    print("✅ 测试数据创建完成:")
    print("  - test_data_nonstandard_1.csv (datetime, lat, lng, velocity, car_id, driver_name)")
    print("  - test_data_nonstandard_2.csv (time, latitude, longitude, speed, vehicle, driver)")
    print("  - test_data_chinese.csv (中文字段名)")
    
    return test_data_1, test_data_2, test_data_3

def test_field_mapping():
    """测试字段映射逻辑"""
    
    print("\n测试字段映射逻辑...")
    
    # 模拟字段映射函数
    def auto_map_fields(available_columns):
        """自动映射字段"""
        mapping = {}
        
        # 时间字段映射
        time_keywords = ['datetime', 'time', 'timestamp', 'date_time', '时间']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in time_keywords):
                mapping['timestamp'] = col
                break
        
        # 纬度字段映射
        lat_keywords = ['lat', 'latitude', '纬度']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in lat_keywords):
                mapping['latitude'] = col
                break
        
        # 经度字段映射
        lon_keywords = ['lng', 'lon', 'longitude', '经度']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in lon_keywords):
                mapping['longitude'] = col
                break
        
        # 速度字段映射
        speed_keywords = ['velocity', 'speed', 'vel', '速度']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in speed_keywords):
                mapping['speed'] = col
                break
        
        # 车辆字段映射
        vehicle_keywords = ['vehicle', 'car_id', 'vehicle_id', '车辆']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in vehicle_keywords):
                mapping['vehicle_id'] = col
                break
        
        # 驾驶员字段映射
        driver_keywords = ['driver', 'driver_id', 'driver_name', '驾驶员']
        for col in available_columns:
            if any(keyword in col.lower() for keyword in driver_keywords):
                mapping['driver_id'] = col
                break
        
        return mapping
    
    # 测试不同的字段名组合
    test_cases = [
        ['datetime', 'lat', 'lng', 'velocity', 'car_id', 'driver_name'],
        ['time', 'latitude', 'longitude', 'speed', 'vehicle', 'driver'],
        ['时间', '纬度', '经度', '速度', '车辆ID', '驾驶员'],
        ['timestamp', 'lat', 'lon', 'vel', 'vehicle_id', 'driver_id']
    ]
    
    for i, columns in enumerate(test_cases, 1):
        print(f"\n测试案例 {i}: {columns}")
        mapping = auto_map_fields(columns)
        print(f"映射结果: {mapping}")
        
        # 检查必需字段
        required_fields = ['timestamp', 'latitude', 'longitude']
        missing = [field for field in required_fields if field not in mapping]
        if missing:
            print(f"❌ 缺少必需字段: {missing}")
        else:
            print("✅ 所有必需字段都已映射")

def main():
    """主函数"""
    print("🚗 车辆轨迹数据字段映射测试")
    print("=" * 50)
    
    # 创建测试数据
    create_test_data()
    
    # 测试字段映射
    test_field_mapping()
    
    print("\n" + "=" * 50)
    print("测试完成！您可以使用生成的CSV文件测试Streamlit应用中的字段映射功能。")
    print("\n使用方法:")
    print("1. 启动Streamlit应用")
    print("2. 进入'车辆轨迹分析'页面")
    print("3. 上传生成的测试CSV文件")
    print("4. 观察字段映射的自动识别效果")

if __name__ == "__main__":
    main()
