import streamlit as st
import importlib
from pathlib import Path

# 配置页面
st.set_page_config(
    page_title="应用程序模板",
    page_icon="🚀",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .sidebar-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2e8b57;
        margin-bottom: 1rem;
    }
    
    .page-section {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
    }
    
    .metric-card {
        background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
        padding: 1rem;
        border-radius: 0.5rem;
        color: white;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .stButton > button {
        width: 100%;
        border-radius: 0.5rem;
        border: none;
        padding: 0.5rem 1rem;
        font-weight: bold;
        transition: all 0.3s;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }
</style>
""", unsafe_allow_html=True)

class AppManager:
    """应用程序管理器"""
    
    def __init__(self):
        self.pages = {
            "dashboard": {
                "name": "📊 仪表板",
                "module": "module-pages.dashboard",
                "group": "主要功能"
            },
            "data_analysis": {
                "name": "📈 数据分析",
                "module": "module-pages.data_analysis",
                "group": "主要功能"
            },
            "vehicle_trajectory_analysis": {
                "name": "🚗 车辆轨迹分析",
                "module": "module-pages.vehicle_trajectory_analysis",
                "group": "专业分析"
            },
            "charts": {
                "name": "📉 图表展示",
                "module": "module-pages.charts", 
                "group": "主要功能"
            },
            "reports": {
                "name": "📋 报告中心",
                "module": "module-pages.reports",
                "group": "管理功能"
            },
            "tools": {
                "name": "🔧 实用工具",
                "module": "module-pages.tools",
                "group": "管理功能"
            },
            "settings": {
                "name": "⚙️ 系统设置",
                "module": "module-pages.settings",
                "group": "管理功能"
            }
        }
        
        # 初始化session state
        if 'current_page' not in st.session_state:
            st.session_state.current_page = 'dashboard'
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.markdown('<div class="sidebar-header">🚀 导航菜单</div>', unsafe_allow_html=True)
            
            # 按组分类显示页面
            groups = {}
            for page_key, page_info in self.pages.items():
                group = page_info["group"]
                if group not in groups:
                    groups[group] = []
                groups[group].append((page_key, page_info))
            
            # 渲染每个组
            for group_name, group_pages in groups.items():
                st.markdown(f"**{group_name}**")
                
                for page_key, page_info in group_pages:
                    # 创建按钮，如果是当前页面则高亮显示
                    button_type = "primary" if st.session_state.current_page == page_key else "secondary"
                    
                    if st.button(
                        page_info["name"], 
                        key=f"btn_{page_key}",
                        type=button_type,
                        use_container_width=True
                    ):
                        st.session_state.current_page = page_key
                        st.rerun()
                
                st.markdown("---")
            
            # 添加一些额外信息
            st.markdown("### 📊 应用信息")
            st.info("版本: v1.0.0")
            st.success("状态: 运行中")
            
            # 添加帮助信息
            with st.expander("❓ 帮助"):
                st.markdown("""
                **使用说明:**
                1. 点击左侧按钮切换页面
                2. 每个页面都有独立的功能
                3. 数据会在页面间保持
                
                **快捷键:**
                - `Ctrl + R`: 刷新页面
                - `F11`: 全屏模式
                """)
    
    def load_page(self, page_key):
        """动态加载页面模块"""
        try:
            page_info = self.pages[page_key]
            module_name = page_info["module"]
            
            # 动态导入模块
            module = importlib.import_module(module_name)
            
            # 调用模块的render函数
            if hasattr(module, 'render'):
                module.render()
            else:
                st.error(f"页面模块 {module_name} 缺少 render() 函数")
                
        except ImportError as e:
            st.error(f"无法加载页面模块: {e}")
            st.markdown("### 🚧 页面开发中...")
            st.info(f"页面 '{page_key}' 正在开发中，请稍后再试。")
            
        except Exception as e:
            st.error(f"页面加载错误: {e}")
    
    def render_main_content(self):
        """渲染主内容区域"""
        current_page = st.session_state.current_page
        page_info = self.pages.get(current_page, {})
        
        # 显示页面标题
        st.markdown(f'<div class="main-header">{page_info.get("name", "未知页面")}</div>', 
                   unsafe_allow_html=True)
        
        # 加载并渲染页面内容
        self.load_page(current_page)
    
    def run(self):
        """运行应用程序"""
        # 渲染侧边栏
        self.render_sidebar()
        
        # 渲染主内容
        self.render_main_content()


def main():
    """主函数"""
    # 创建应用管理器
    app = AppManager()
    
    # 运行应用
    app.run()


if __name__ == "__main__":
    main()
