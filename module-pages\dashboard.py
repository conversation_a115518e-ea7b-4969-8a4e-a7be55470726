import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timed<PERSON><PERSON>

def render():
    """渲染仪表板页面"""
    
    # 页面描述
    st.markdown("""
    ### 📊 欢迎来到应用程序仪表板
    
    这里展示了系统的关键指标和概览信息，帮助您快速了解当前状态。
    """)
    
    # 创建指标卡片
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            label="📈 总用户数",
            value="12,345",
            delta="234",
            delta_color="normal"
        )
    
    with col2:
        st.metric(
            label="🔥 活跃用户",
            value="8,567",
            delta="156",
            delta_color="normal"
        )
    
    with col3:
        st.metric(
            label="💰 总收入",
            value="¥89,012",
            delta="¥5,432",
            delta_color="normal"
        )
    
    with col4:
        st.metric(
            label="📊 转化率",
            value="12.5%",
            delta="2.1%",
            delta_color="normal"
        )
    
    st.markdown("---")
    
    # 创建两列布局
    col1, col2 = st.columns([2, 1])
    
    with col1:
        st.subheader("📈 用户增长趋势")
        
        # 生成示例数据
        dates = pd.date_range(start='2024-01-01', end='2024-12-31', freq='D')
        np.random.seed(42)
        users = np.cumsum(np.random.randint(10, 100, len(dates))) + 1000
        
        df = pd.DataFrame({
            'date': dates,
            'users': users
        })
        
        # 创建折线图
        fig = px.line(df, x='date', y='users', 
                     title='用户增长趋势',
                     labels={'date': '日期', 'users': '用户数量'})
        fig.update_layout(height=400)
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("🎯 今日任务")
        
        # 任务列表
        tasks = [
            {"task": "审核用户反馈", "status": "✅", "priority": "高"},
            {"task": "更新产品文档", "status": "🔄", "priority": "中"},
            {"task": "数据备份检查", "status": "⏳", "priority": "高"},
            {"task": "性能监控报告", "status": "✅", "priority": "低"},
            {"task": "安全漏洞扫描", "status": "🔄", "priority": "高"},
        ]
        
        for task in tasks:
            priority_color = {
                "高": "🔴",
                "中": "🟡", 
                "低": "🟢"
            }
            
            st.markdown(f"""
            <div style="
                background-color: #f8f9fa;
                padding: 0.5rem;
                border-radius: 0.25rem;
                margin: 0.25rem 0;
                border-left: 3px solid #007bff;
            ">
                {task['status']} {task['task']}<br>
                <small>{priority_color[task['priority']]} {task['priority']}优先级</small>
            </div>
            """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # 数据表格部分
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("📊 最近活动")
        
        # 生成示例活动数据
        activities = pd.DataFrame({
            '时间': [
                '10:30', '10:15', '09:45', '09:30', '09:00'
            ],
            '用户': [
                'user_001', 'user_002', 'user_003', 'user_004', 'user_005'
            ],
            '操作': [
                '登录系统', '上传文件', '生成报告', '修改设置', '查看数据'
            ],
            '状态': [
                '成功', '成功', '失败', '成功', '成功'
            ]
        })
        
        # 添加状态颜色
        def color_status(val):
            color = 'green' if val == '成功' else 'red'
            return f'color: {color}'
        
        styled_df = activities.style.applymap(color_status, subset=['状态'])
        st.dataframe(styled_df, use_container_width=True)
    
    with col2:
        st.subheader("🌍 地区分布")
        
        # 创建饼图
        regions = ['北京', '上海', '广州', '深圳', '杭州', '其他']
        values = [25, 20, 15, 12, 10, 18]
        
        fig = go.Figure(data=[go.Pie(
            labels=regions, 
            values=values,
            hole=0.4,
            textinfo='label+percent'
        )])
        
        fig.update_layout(
            title="用户地区分布",
            height=300,
            showlegend=True
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # 快速操作按钮
    st.markdown("---")
    st.subheader("⚡ 快速操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🔄 刷新数据", use_container_width=True):
            st.success("数据已刷新！")
            st.rerun()
    
    with col2:
        if st.button("📊 生成报告", use_container_width=True):
            st.info("正在生成报告...")
            # 这里可以添加报告生成逻辑
    
    with col3:
        if st.button("📧 发送通知", use_container_width=True):
            st.success("通知已发送！")
    
    with col4:
        if st.button("⚙️ 系统设置", use_container_width=True):
            st.session_state.current_page = 'settings'
            st.rerun()
    
    # 系统状态
    st.markdown("---")
    st.subheader("🖥️ 系统状态")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        **服务器状态**
        - CPU使用率: 45%
        - 内存使用率: 67%
        - 磁盘使用率: 23%
        """)
    
    with col2:
        st.markdown("""
        **数据库状态**
        - 连接数: 45/100
        - 查询响应时间: 12ms
        - 数据大小: 2.3GB
        """)
    
    with col3:
        st.markdown("""
        **网络状态**
        - 上行带宽: 45 Mbps
        - 下行带宽: 89 Mbps
        - 延迟: 8ms
        """)
