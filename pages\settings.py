import streamlit as st
import json
from datetime import datetime

def render():
    """渲染系统设置页面"""
    
    st.markdown("""
    ### ⚙️ 系统设置
    
    在这里您可以配置应用程序的各种设置和偏好。
    """)
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["👤 用户设置", "🎨 界面设置", "🔔 通知设置", "🔧 系统配置"])
    
    with tab1:
        st.subheader("👤 用户设置")
        
        col1, col2 = st.columns([1, 2])
        
        with col1:
            st.subheader("📸 头像设置")
            
            # 头像上传
            avatar_file = st.file_uploader(
                "上传头像",
                type=['png', 'jpg', 'jpeg'],
                help="支持PNG、JPG格式，建议尺寸200x200像素"
            )
            
            if avatar_file:
                st.image(avatar_file, width=150, caption="预览头像")
            else:
                # 显示默认头像
                st.markdown("""
                <div style="
                    width: 150px; 
                    height: 150px; 
                    background-color: #f0f0f0; 
                    border-radius: 50%; 
                    display: flex; 
                    align-items: center; 
                    justify-content: center;
                    font-size: 48px;
                    color: #666;
                ">👤</div>
                """, unsafe_allow_html=True)
        
        with col2:
            st.subheader("📝 基本信息")
            
            # 用户信息表单
            with st.form("user_info_form"):
                username = st.text_input("用户名", value="admin", help="用于登录的用户名")
                display_name = st.text_input("显示名称", value="管理员", help="在界面中显示的名称")
                email = st.text_input("邮箱地址", value="<EMAIL>")
                phone = st.text_input("手机号码", value="", placeholder="请输入手机号码")
                
                department = st.selectbox(
                    "部门",
                    ["技术部", "产品部", "运营部", "市场部", "财务部", "人事部"]
                )
                
                role = st.selectbox(
                    "角色",
                    ["管理员", "普通用户", "只读用户", "审核员"]
                )
                
                bio = st.text_area(
                    "个人简介",
                    placeholder="简单介绍一下自己...",
                    height=100
                )
                
                if st.form_submit_button("💾 保存用户信息", type="primary"):
                    st.success("✅ 用户信息已保存！")
        
        st.markdown("---")
        
        # 密码修改
        st.subheader("🔐 密码修改")
        
        col1, col2 = st.columns(2)
        
        with col1:
            with st.form("password_form"):
                current_password = st.text_input("当前密码", type="password")
                new_password = st.text_input("新密码", type="password")
                confirm_password = st.text_input("确认新密码", type="password")
                
                if st.form_submit_button("🔄 修改密码"):
                    if new_password != confirm_password:
                        st.error("❌ 两次输入的密码不一致")
                    elif len(new_password) < 6:
                        st.error("❌ 密码长度至少6位")
                    else:
                        st.success("✅ 密码修改成功！")
        
        with col2:
            st.subheader("🔒 安全设置")
            
            two_factor_auth = st.checkbox("启用双因素认证", value=False)
            
            if two_factor_auth:
                st.info("📱 请使用手机应用扫描二维码设置双因素认证")
                # 这里可以显示二维码
                st.code("二维码占位符")
            
            login_notifications = st.checkbox("登录通知", value=True, help="有新设备登录时发送通知")
            auto_logout = st.number_input("自动登出时间(分钟)", value=30, min_value=5, max_value=480)
    
    with tab2:
        st.subheader("🎨 界面设置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("🌈 主题设置")
            
            theme = st.selectbox(
                "选择主题",
                ["浅色主题", "深色主题", "自动切换"],
                help="选择您喜欢的界面主题"
            )
            
            primary_color = st.color_picker("主色调", "#1f77b4")
            
            font_size = st.selectbox(
                "字体大小",
                ["小", "中", "大", "特大"],
                index=1
            )
            
            sidebar_position = st.radio(
                "侧边栏位置",
                ["左侧", "右侧"],
                horizontal=True
            )
            
            compact_mode = st.checkbox("紧凑模式", value=False, help="减少界面元素间距")
        
        with col2:
            st.subheader("📊 显示设置")
            
            show_tooltips = st.checkbox("显示工具提示", value=True)
            show_animations = st.checkbox("启用动画效果", value=True)
            
            default_page = st.selectbox(
                "默认首页",
                ["仪表板", "数据分析", "图表展示", "报告中心", "实用工具"]
            )
            
            items_per_page = st.number_input(
                "每页显示条数",
                value=20,
                min_value=10,
                max_value=100,
                step=10
            )
            
            auto_refresh = st.checkbox("自动刷新数据", value=False)
            
            if auto_refresh:
                refresh_interval = st.selectbox(
                    "刷新间隔",
                    ["30秒", "1分钟", "5分钟", "10分钟", "30分钟"],
                    index=2
                )
        
        # 预览区域
        st.markdown("---")
        st.subheader("🔍 主题预览")
        
        preview_style = f"""
        <div style="
            background: {'#ffffff' if theme == '浅色主题' else '#1e1e1e'};
            color: {'#000000' if theme == '浅色主题' else '#ffffff'};
            padding: 20px;
            border-radius: 10px;
            border: 2px solid {primary_color};
            margin: 10px 0;
        ">
            <h3 style="color: {primary_color};">预览标题</h3>
            <p>这是一个主题预览示例，展示了当前选择的颜色和主题效果。</p>
            <button style="
                background-color: {primary_color};
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
            ">示例按钮</button>
        </div>
        """
        st.markdown(preview_style, unsafe_allow_html=True)
    
    with tab3:
        st.subheader("🔔 通知设置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("📧 邮件通知")
            
            email_notifications = st.checkbox("启用邮件通知", value=True)
            
            if email_notifications:
                st.multiselect(
                    "邮件通知类型",
                    ["系统更新", "数据报告", "安全警告", "任务完成", "错误提醒"],
                    default=["安全警告", "错误提醒"]
                )
                
                email_frequency = st.selectbox(
                    "邮件频率",
                    ["实时", "每小时汇总", "每日汇总", "每周汇总"]
                )
            
            st.subheader("📱 推送通知")
            
            push_notifications = st.checkbox("启用推送通知", value=True)
            
            if push_notifications:
                st.multiselect(
                    "推送通知类型",
                    ["重要提醒", "任务更新", "系统消息", "用户消息"],
                    default=["重要提醒", "系统消息"]
                )
                
                quiet_hours = st.checkbox("免打扰时间", value=False)
                
                if quiet_hours:
                    col_a, col_b = st.columns(2)
                    with col_a:
                        start_time = st.time_input("开始时间", value=datetime.strptime("22:00", "%H:%M").time())
                    with col_b:
                        end_time = st.time_input("结束时间", value=datetime.strptime("08:00", "%H:%M").time())
        
        with col2:
            st.subheader("🔊 声音设置")
            
            sound_enabled = st.checkbox("启用声音提醒", value=True)
            
            if sound_enabled:
                notification_sound = st.selectbox(
                    "通知声音",
                    ["默认", "铃声1", "铃声2", "铃声3", "自定义"]
                )
                
                sound_volume = st.slider("音量", 0, 100, 50)
            
            st.subheader("📊 通知历史")
            
            keep_history = st.checkbox("保留通知历史", value=True)
            
            if keep_history:
                history_days = st.number_input(
                    "保留天数",
                    value=30,
                    min_value=1,
                    max_value=365
                )
            
            # 测试通知
            st.markdown("---")
            st.subheader("🧪 测试通知")
            
            if st.button("📧 发送测试邮件"):
                st.success("✅ 测试邮件已发送！")
            
            if st.button("📱 发送测试推送"):
                st.success("✅ 测试推送已发送！")
    
    with tab4:
        st.subheader("🔧 系统配置")
        
        col1, col2 = st.columns(2)
        
        with col1:
            st.subheader("💾 数据设置")
            
            auto_backup = st.checkbox("自动备份", value=True)
            
            if auto_backup:
                backup_frequency = st.selectbox(
                    "备份频率",
                    ["每日", "每周", "每月"],
                    index=1
                )
                
                backup_location = st.selectbox(
                    "备份位置",
                    ["本地存储", "云端存储", "网络驱动器"]
                )
                
                backup_retention = st.number_input(
                    "备份保留数量",
                    value=10,
                    min_value=1,
                    max_value=100
                )
            
            data_compression = st.checkbox("启用数据压缩", value=True)
            
            cache_size = st.selectbox(
                "缓存大小",
                ["128MB", "256MB", "512MB", "1GB", "2GB"],
                index=2
            )
        
        with col2:
            st.subheader("🌐 网络设置")
            
            proxy_enabled = st.checkbox("使用代理服务器", value=False)
            
            if proxy_enabled:
                proxy_host = st.text_input("代理服务器地址")
                proxy_port = st.number_input("端口", value=8080)
                proxy_auth = st.checkbox("需要认证")
                
                if proxy_auth:
                    proxy_username = st.text_input("用户名")
                    proxy_password = st.text_input("密码", type="password")
            
            timeout_settings = st.number_input(
                "请求超时时间(秒)",
                value=30,
                min_value=5,
                max_value=300
            )
            
            max_connections = st.number_input(
                "最大连接数",
                value=100,
                min_value=10,
                max_value=1000
            )
        
        # 系统信息
        st.markdown("---")
        st.subheader("ℹ️ 系统信息")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("应用版本", "v1.0.0")
            st.metric("Python版本", "3.11.0")
        
        with col2:
            st.metric("Streamlit版本", "1.46.1")
            st.metric("运行时间", "2小时30分")
        
        with col3:
            st.metric("内存使用", "256 MB")
            st.metric("磁盘空间", "2.3 GB")
        
        # 操作按钮
        st.markdown("---")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            if st.button("💾 保存所有设置", type="primary"):
                st.success("✅ 所有设置已保存！")
        
        with col2:
            if st.button("🔄 重置为默认"):
                st.warning("⚠️ 设置已重置为默认值")
        
        with col3:
            if st.button("📤 导出设置"):
                settings_data = {
                    "theme": theme,
                    "primary_color": primary_color,
                    "font_size": font_size,
                    "notifications": email_notifications,
                    "auto_backup": auto_backup
                }
                st.download_button(
                    "📥 下载配置文件",
                    json.dumps(settings_data, indent=2),
                    "settings.json",
                    "application/json"
                )
        
        with col4:
            uploaded_settings = st.file_uploader(
                "📤 导入设置",
                type=['json'],
                help="上传之前导出的设置文件"
            )
            
            if uploaded_settings:
                try:
                    settings_data = json.load(uploaded_settings)
                    st.success("✅ 设置导入成功！")
                except Exception as e:
                    st.error(f"❌ 导入失败: {e}")
