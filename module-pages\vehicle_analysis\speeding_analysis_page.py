"""
超速分析页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from .base_page import BasePage, UtilityFunctions


class SpeedingAnalysisPage(BasePage):
    """超速分析页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "⚡ 超速分析"
        self.description = "车辆超速行为分析和统计"
    
    def render(self):
        """渲染超速分析页面"""
        st.subheader(self.title)
        
        if not self.has_data():
            st.warning("⚠️ 请先在'数据导入'页面上传数据")
            return
        
        df = self.get_data()
        
        if 'speed' not in df.columns:
            st.error("❌ 数据中缺少速度字段，无法进行超速分析")
            return
        
        # 显示数据信息
        self.show_data_info(df)
        
        # 超速分析参数设置
        self._show_analysis_settings()
        
        # 执行超速分析
        self._perform_speeding_analysis(df)
    
    def _show_analysis_settings(self):
        """显示分析参数设置"""
        st.subheader("⚙️ 分析参数")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            speed_limit = st.number_input(
                "速度限制 (km/h)", 
                min_value=20, 
                max_value=120, 
                value=st.session_state.get('speed_limit', 60),
                help="超过此速度将被认定为超速"
            )
            st.session_state.speed_limit = speed_limit
        
        with col2:
            min_duration = st.number_input(
                "最小超速时长 (秒)", 
                min_value=1, 
                max_value=300, 
                value=5,
                help="持续超速时间少于此值的事件将被忽略"
            )
        
        with col3:
            severity_threshold = st.number_input(
                "严重超速阈值 (km/h)", 
                min_value=speed_limit + 5, 
                max_value=150, 
                value=speed_limit + 20,
                help="超过此速度将被认定为严重超速"
            )
        
        return speed_limit, min_duration, severity_threshold
    
    def _perform_speeding_analysis(self, df):
        """执行超速分析"""
        speed_limit, min_duration, severity_threshold = self._show_analysis_settings()
        
        # 检测超速事件
        speeding_events = UtilityFunctions.detect_speeding_events(df, speed_limit)
        
        # 过滤短时间超速事件
        filtered_events = [event for event in speeding_events if event['duration'] >= min_duration]
        
        # 分类超速事件
        mild_events = [event for event in filtered_events if event['max_speed'] < severity_threshold]
        severe_events = [event for event in filtered_events if event['max_speed'] >= severity_threshold]
        
        # 显示超速统计
        self._show_speeding_statistics(df, filtered_events, mild_events, severe_events, speed_limit)
        
        # 显示超速事件详情
        self._show_speeding_events(filtered_events, speed_limit, severity_threshold)
        
        # 显示超速分析图表
        self._show_speeding_charts(df, filtered_events, speed_limit)
    
    def _show_speeding_statistics(self, df, all_events, mild_events, severe_events, speed_limit):
        """显示超速统计信息"""
        st.subheader("📊 超速统计")
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            speeding_points = len(df[df['speed'] > speed_limit])
            speeding_rate = (speeding_points / len(df)) * 100
            st.metric("超速率", f"{speeding_rate:.1f}%")
        
        with col2:
            st.metric("超速事件数", len(all_events))
        
        with col3:
            st.metric("轻微超速", len(mild_events), delta=f"{len(mild_events)/(len(all_events)+1)*100:.0f}%")
        
        with col4:
            st.metric("严重超速", len(severe_events), delta=f"{len(severe_events)/(len(all_events)+1)*100:.0f}%")
        
        # 详细统计
        if all_events:
            total_speeding_time = sum([event['duration'] for event in all_events]) / 60  # 分钟
            avg_speeding_duration = np.mean([event['duration'] for event in all_events])
            max_speed = max([event['max_speed'] for event in all_events])
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("总超速时间", f"{total_speeding_time:.1f} 分钟")
            
            with col2:
                st.metric("平均超速时长", f"{avg_speeding_duration:.1f} 秒")
            
            with col3:
                st.metric("最高速度", f"{max_speed:.1f} km/h")
    
    def _show_speeding_events(self, events, speed_limit, severity_threshold):
        """显示超速事件详情"""
        st.subheader("📋 超速事件详情")
        
        if not events:
            st.info("✅ 未检测到超速事件")
            return
        
        # 创建事件数据框
        events_data = []
        for i, event in enumerate(events):
            severity = "严重" if event['max_speed'] >= severity_threshold else "轻微"
            events_data.append({
                "事件编号": i + 1,
                "开始时间": event['start_time'].strftime('%Y-%m-%d %H:%M:%S'),
                "结束时间": event['end_time'].strftime('%Y-%m-%d %H:%M:%S'),
                "持续时间(秒)": f"{event['duration']:.1f}",
                "最高速度(km/h)": f"{event['max_speed']:.1f}",
                "平均速度(km/h)": f"{event['avg_speed']:.1f}",
                "超速程度": f"{event['max_speed'] - speed_limit:.1f}",
                "严重程度": severity,
                "起始位置": f"{event['start_lat']:.6f}, {event['start_lon']:.6f}",
                "结束位置": f"{event['end_lat']:.6f}, {event['end_lon']:.6f}"
            })
        
        events_df = pd.DataFrame(events_data)
        
        # 显示事件表格
        st.dataframe(
            events_df,
            use_container_width=True,
            column_config={
                "严重程度": st.column_config.TextColumn(
                    "严重程度",
                    help="基于最高速度的超速严重程度"
                )
            }
        )
        
        # 导出功能
        if st.button("📥 导出超速事件报告"):
            csv = events_df.to_csv(index=False, encoding='utf-8-sig')
            st.download_button(
                label="下载CSV文件",
                data=csv,
                file_name=f"speeding_events_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.csv",
                mime="text/csv"
            )
    
    def _show_speeding_charts(self, df, events, speed_limit):
        """显示超速分析图表"""
        st.subheader("📈 超速分析图表")
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('速度时间序列', '速度分布', '超速事件时长分布', '超速严重程度分布'),
            specs=[[{"secondary_y": False}, {"secondary_y": False}],
                   [{"secondary_y": False}, {"secondary_y": False}]]
        )
        
        # 1. 速度时间序列
        if 'timestamp' in df.columns:
            # 采样数据以提高性能
            sample_df = df.sample(min(1000, len(df))).sort_values('timestamp')
            
            fig.add_trace(
                go.Scatter(
                    x=sample_df['timestamp'],
                    y=sample_df['speed'],
                    mode='lines',
                    name='速度',
                    line=dict(color='blue', width=1)
                ),
                row=1, col=1
            )
            
            # 添加速度限制线
            fig.add_hline(
                y=speed_limit,
                line_dash="dash",
                line_color="red",
                annotation_text=f"速度限制: {speed_limit} km/h",
                row=1, col=1
            )
        
        # 2. 速度分布直方图
        fig.add_trace(
            go.Histogram(
                x=df['speed'],
                nbinsx=50,
                name='速度分布',
                marker_color='lightblue'
            ),
            row=1, col=2
        )
        
        # 添加速度限制线
        fig.add_vline(
            x=speed_limit,
            line_dash="dash",
            line_color="red",
            row=1, col=2
        )
        
        # 3. 超速事件时长分布
        if events:
            durations = [event['duration'] for event in events]
            fig.add_trace(
                go.Histogram(
                    x=durations,
                    nbinsx=20,
                    name='超速时长分布',
                    marker_color='orange'
                ),
                row=2, col=1
            )
        
        # 4. 超速严重程度分布
        if events:
            severity_levels = [event['max_speed'] - speed_limit for event in events]
            fig.add_trace(
                go.Histogram(
                    x=severity_levels,
                    nbinsx=20,
                    name='超速程度分布',
                    marker_color='red'
                ),
                row=2, col=2
            )
        
        # 更新布局
        fig.update_layout(
            height=800,
            showlegend=False,
            title_text="超速分析综合图表"
        )
        
        # 更新坐标轴标签
        fig.update_xaxes(title_text="时间", row=1, col=1)
        fig.update_yaxes(title_text="速度 (km/h)", row=1, col=1)
        
        fig.update_xaxes(title_text="速度 (km/h)", row=1, col=2)
        fig.update_yaxes(title_text="频次", row=1, col=2)
        
        fig.update_xaxes(title_text="持续时间 (秒)", row=2, col=1)
        fig.update_yaxes(title_text="事件数", row=2, col=1)
        
        fig.update_xaxes(title_text="超速程度 (km/h)", row=2, col=2)
        fig.update_yaxes(title_text="事件数", row=2, col=2)
        
        st.plotly_chart(fig, use_container_width=True)
        
        # 时段分析
        self._show_time_analysis(df, speed_limit)
    
    def _show_time_analysis(self, df, speed_limit):
        """显示时段分析"""
        st.subheader("⏰ 超速时段分析")
        
        if 'timestamp' not in df.columns:
            st.warning("⚠️ 缺少时间戳字段，无法进行时段分析")
            return
        
        # 添加时间特征
        df_time = df.copy()
        df_time['hour'] = df_time['timestamp'].dt.hour
        df_time['day_of_week'] = df_time['timestamp'].dt.day_name()
        df_time['is_speeding'] = df_time['speed'] > speed_limit
        
        col1, col2 = st.columns(2)
        
        with col1:
            # 按小时统计超速率
            hourly_speeding = df_time.groupby('hour').agg({
                'is_speeding': ['count', 'sum']
            }).round(2)
            hourly_speeding.columns = ['总记录数', '超速记录数']
            hourly_speeding['超速率(%)'] = (hourly_speeding['超速记录数'] / hourly_speeding['总记录数'] * 100).round(1)
            
            fig_hour = px.bar(
                x=hourly_speeding.index,
                y=hourly_speeding['超速率(%)'],
                title="各时段超速率",
                labels={'x': '小时', 'y': '超速率 (%)'}
            )
            st.plotly_chart(fig_hour, use_container_width=True)
        
        with col2:
            # 按星期统计超速率
            weekly_speeding = df_time.groupby('day_of_week').agg({
                'is_speeding': ['count', 'sum']
            }).round(2)
            weekly_speeding.columns = ['总记录数', '超速记录数']
            weekly_speeding['超速率(%)'] = (weekly_speeding['超速记录数'] / weekly_speeding['总记录数'] * 100).round(1)
            
            # 重新排序星期
            day_order = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
            weekly_speeding = weekly_speeding.reindex([day for day in day_order if day in weekly_speeding.index])
            
            fig_week = px.bar(
                x=weekly_speeding.index,
                y=weekly_speeding['超速率(%)'],
                title="各星期超速率",
                labels={'x': '星期', 'y': '超速率 (%)'}
            )
            st.plotly_chart(fig_week, use_container_width=True)
