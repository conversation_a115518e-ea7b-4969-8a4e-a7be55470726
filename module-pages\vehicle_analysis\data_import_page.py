"""
数据导入页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
from .base_page import BasePage, UtilityFunctions


class DataImportPage(BasePage):
    """数据导入页面"""
    
    def __init__(self):
        super().__init__()
        self.title = "📁 数据导入"
        self.description = "车辆轨迹数据导入和预处理"
    
    def render(self):
        """渲染数据导入页面"""
        st.subheader(self.title)
        
        # 数据格式说明
        self._show_format_info()
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择CSV文件", 
            type=['csv'],
            help="支持CSV格式的轨迹数据文件"
        )
        
        # 参数设置
        col1, col2 = st.columns(2)
        with col1:
            sample_rate = st.slider("数据采样率（%）", 1, 100, 100,
                                  help="大数据量时可降低采样率提高处理速度")
        with col2:
            speed_limit = st.number_input("速度限制（km/h）", 20, 120, 60,
                                        help="用于超速分析的速度阈值")
        
        if uploaded_file is not None:
            self._process_uploaded_file(uploaded_file, sample_rate, speed_limit)
        
        # 示例数据生成
        self._show_sample_data_generator()
    
    def _show_format_info(self):
        """显示数据格式说明"""
        with st.expander("📋 数据格式要求", expanded=False):
            st.markdown("""
            **支持的数据格式：**
            - CSV文件格式
            - 必需字段：`timestamp`, `latitude`, `longitude`
            - 可选字段：`speed`, `vehicle_id`, `driver_id`
            
            **示例数据格式：**
            ```
            timestamp,latitude,longitude,speed,vehicle_id
            2024-01-01 08:00:00,39.9042,116.4074,45.5,V001
            2024-01-01 08:00:30,39.9045,116.4078,52.3,V001
            ```
            
            **字段说明：**
            - `timestamp`: 时间戳（支持多种格式）
            - `latitude`: 纬度（WGS84坐标系）
            - `longitude`: 经度（WGS84坐标系）
            - `speed`: 速度（km/h，可选）
            - `vehicle_id`: 车辆ID（可选）
            - `driver_id`: 驾驶员ID（可选）
            """)
    
    def _process_uploaded_file(self, uploaded_file, sample_rate, speed_limit):
        """处理上传的文件"""
        try:
            # 显示文件信息
            file_size = uploaded_file.size / (1024 * 1024)  # MB
            st.info(f"📁 文件大小: {file_size:.2f} MB")

            # 读取数据
            with st.spinner("正在读取数据..."):
                df = pd.read_csv(uploaded_file)
                
            st.success(f"✅ 数据读取成功！共 {len(df):,} 条记录")
            
            # 显示数据预览
            st.subheader("📊 数据预览")
            st.dataframe(df.head(10), use_container_width=True)
            
            # 字段验证
            required_fields = ['timestamp', 'latitude', 'longitude']
            if not self.validate_required_fields(df, required_fields):
                st.stop()
            
            # 数据处理
            self._process_data(df, sample_rate, speed_limit)
            
        except Exception as e:
            st.error(f"❌ 文件处理失败: {str(e)}")
    
    def _process_data(self, df, sample_rate, speed_limit):
        """处理数据"""
        with st.spinner("正在处理数据..."):
            # 数据类型转换和验证
            try:
                # 时间戳处理
                if 'timestamp' in df.columns:
                    df['timestamp'] = pd.to_datetime(df['timestamp'])
                    df = df.sort_values('timestamp').reset_index(drop=True)
                    st.info("✅ 时间戳字段处理完成")

                # 坐标验证
                if 'latitude' in df.columns and 'longitude' in df.columns:
                    # 检查坐标范围
                    lat_range = (df['latitude'].min(), df['latitude'].max())
                    lon_range = (df['longitude'].min(), df['longitude'].max())
                    
                    if not (-90 <= lat_range[0] <= lat_range[1] <= 90):
                        st.warning(f"⚠️ 纬度范围异常: {lat_range}")
                    if not (-180 <= lon_range[0] <= lon_range[1] <= 180):
                        st.warning(f"⚠️ 经度范围异常: {lon_range}")

                # 数据采样
                if sample_rate < 100:
                    df = df.sample(frac=sample_rate/100).sort_values('timestamp').reset_index(drop=True)
                    st.info(f"🔄 已应用 {sample_rate}% 采样率，当前数据量: {len(df):,} 条")

                # 计算速度（如果没有速度字段）
                if 'speed' not in df.columns and 'latitude' in df.columns and 'longitude' in df.columns and len(df) > 1:
                    with st.spinner("正在计算速度..."):
                        df['speed'] = UtilityFunctions.calculate_speed(df)
                        st.info("✅ 速度计算完成")

                # 计算距离
                if 'latitude' in df.columns and 'longitude' in df.columns and len(df) > 1:
                    with st.spinner("正在计算距离..."):
                        distances = []
                        for i in range(len(df)):
                            if i == 0:
                                distances.append(0)
                            else:
                                dist = UtilityFunctions.calculate_distance(
                                    df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                                    df.iloc[i]['latitude'], df.iloc[i]['longitude']
                                )
                                distances.append(dist)
                        df['distance'] = distances
                        st.info("✅ 距离计算完成")
                
                # 保存到session state
                self.set_data(df)
                st.session_state.speed_limit = speed_limit
                
                st.success("✅ 数据处理完成！")
                
                # 显示处理后的数据信息
                self.show_data_info(df)
                
            except Exception as e:
                st.error(f"❌ 数据处理失败: {str(e)}")
    
    def _show_sample_data_generator(self):
        """显示示例数据生成器"""
        st.subheader("🎲 生成示例数据")
        
        col1, col2 = st.columns(2)
        with col1:
            n_points = st.number_input("数据点数量", 50, 1000, 200)
        with col2:
            speed_limit = st.number_input("示例速度限制", 40, 100, 60)
        
        if st.button("🚀 生成示例数据", type="primary"):
            with st.spinner("正在生成示例数据..."):
                # 模拟北京市区轨迹
                start_lat, start_lon = 39.9042, 116.4074
                
                timestamps = pd.date_range('2024-01-01 08:00:00', periods=n_points, freq='30S')
                
                # 生成轨迹点
                lats, lons, speeds = [start_lat], [start_lon], [0]
                
                for i in range(1, n_points):
                    # 随机移动
                    lat_change = np.random.normal(0, 0.001)
                    lon_change = np.random.normal(0, 0.001)
                    
                    new_lat = lats[-1] + lat_change
                    new_lon = lons[-1] + lon_change
                    
                    lats.append(new_lat)
                    lons.append(new_lon)
                    
                    # 生成速度（包含一些超速情况）
                    if np.random.random() < 0.1:  # 10%概率超速
                        speed = np.random.uniform(65, 90)
                    else:
                        speed = np.random.uniform(20, 55)
                    speeds.append(speed)
                
                df = pd.DataFrame({
                    'timestamp': timestamps,
                    'latitude': lats,
                    'longitude': lons,
                    'speed': speeds,
                    'vehicle_id': ['V001'] * n_points
                })
                
                # 计算距离
                distances = []
                for i in range(len(df)):
                    if i == 0:
                        distances.append(0)
                    else:
                        dist = UtilityFunctions.calculate_distance(
                            df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                            df.iloc[i]['latitude'], df.iloc[i]['longitude']
                        )
                        distances.append(dist)
                df['distance'] = distances
                
                self.set_data(df)
                st.session_state.speed_limit = speed_limit
            
            st.success("✅ 示例数据生成成功！")
            st.rerun()
