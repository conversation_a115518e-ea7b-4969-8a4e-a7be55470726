# 🔗 智能字段映射功能

## 功能概述

为车辆轨迹数据分析工具新增了智能字段映射功能，解决了用户CSV文件字段名不标准的问题。系统可以自动识别常见的字段名变体，并提供直观的映射界面。

## 主要特性

### ✨ 自动字段识别
- **智能匹配**: 自动识别常见的字段名变体
- **多语言支持**: 支持中英文字段名
- **模糊匹配**: 基于关键词的智能匹配算法

### 🎯 用户友好界面
- **可视化映射**: 直观的下拉选择界面
- **颜色标识**: 红色标记必需字段，黄色标记可选字段
- **实时预览**: 显示映射结果和示例数据
- **验证提示**: 自动检查必需字段完整性

### 🔍 数据质量检查
- **坐标验证**: 检查纬度(-90~90)和经度(-180~180)范围
- **速度验证**: 检查速度值合理性(0~300 km/h)
- **时间格式**: 自动转换时间戳格式
- **缺失值处理**: 识别和报告数据质量问题

## 支持的字段映射

### 必需字段 🔴

| 标准字段 | 支持的变体 | 说明 |
|---------|-----------|------|
| `timestamp` | `datetime`, `time`, `date_time`, `时间` | 时间戳字段 |
| `latitude` | `lat`, `latitude`, `纬度` | 纬度坐标 |
| `longitude` | `lng`, `lon`, `longitude`, `经度` | 经度坐标 |

### 可选字段 🟡

| 标准字段 | 支持的变体 | 说明 |
|---------|-----------|------|
| `speed` | `velocity`, `speed`, `vel`, `速度` | 车辆速度 |
| `vehicle_id` | `vehicle`, `car_id`, `vehicle_id`, `车辆ID` | 车辆标识 |
| `driver_id` | `driver`, `driver_id`, `driver_name`, `驾驶员` | 驾驶员标识 |
| `altitude` | `alt`, `altitude`, `elevation`, `海拔` | 海拔高度 |
| `heading` | `head`, `heading`, `bearing`, `方向` | 行驶方向 |

## 使用流程

### 1. 文件上传
```
📁 选择CSV文件 → 系统读取文件头部 → 显示可用字段
```

### 2. 字段映射
```
🔗 自动识别字段 → 用户确认/调整映射 → 预览映射结果
```

### 3. 数据验证
```
✅ 检查必需字段 → 验证数据格式 → 生成质量报告
```

### 4. 数据处理
```
🚀 应用字段映射 → 数据类型转换 → 计算衍生字段
```

## 测试数据

系统提供了多种格式的测试数据文件：

### 测试文件1: `test_data_nonstandard_1.csv`
```csv
datetime,lat,lng,velocity,car_id,driver_name
2024-01-01 08:00:00,39.9056,116.4078,62.16,V001,张三
```

### 测试文件2: `test_data_nonstandard_2.csv`
```csv
time,latitude,longitude,speed,vehicle,driver
2024-01-01 08:00:00,39.9056,116.4078,62.16,V001,张三
```

### 测试文件3: `test_data_chinese.csv`
```csv
时间,纬度,经度,速度,车辆ID,驾驶员
2024-01-01 08:00:00,39.9056,116.4078,62.16,V001,张三
```

## 技术实现

### 自动识别算法
```python
def auto_map_fields(available_columns):
    """智能字段映射算法"""
    mapping = {}
    
    # 关键词匹配
    field_keywords = {
        'timestamp': ['datetime', 'time', 'timestamp', '时间'],
        'latitude': ['lat', 'latitude', '纬度'],
        'longitude': ['lng', 'lon', 'longitude', '经度'],
        # ... 更多字段
    }
    
    for standard_field, keywords in field_keywords.items():
        for col in available_columns:
            if any(keyword in col.lower() for keyword in keywords):
                mapping[standard_field] = col
                break
    
    return mapping
```

### 数据验证逻辑
```python
def validate_data(df, field_mapping):
    """数据质量验证"""
    issues = []
    
    # 坐标范围检查
    if 'latitude' in df.columns:
        invalid_lat = ((df['latitude'] < -90) | (df['latitude'] > 90)).sum()
        if invalid_lat > 0:
            issues.append(f"发现 {invalid_lat} 个异常纬度值")
    
    # 速度合理性检查
    if 'speed' in df.columns:
        invalid_speed = ((df['speed'] < 0) | (df['speed'] > 300)).sum()
        if invalid_speed > 0:
            issues.append(f"发现 {invalid_speed} 个异常速度值")
    
    return issues
```

## 优势特点

### 🎯 用户体验
- **零学习成本**: 无需了解标准字段名
- **自动化程度高**: 大部分情况下无需手动调整
- **错误提示清晰**: 明确指出缺失或异常的字段

### 🔧 技术优势
- **兼容性强**: 支持各种CSV格式和字段命名
- **扩展性好**: 易于添加新的字段类型和识别规则
- **性能优化**: 只读取文件头部进行字段识别

### 📊 数据质量
- **全面验证**: 多维度数据质量检查
- **智能修复**: 自动处理常见数据问题
- **详细报告**: 提供完整的数据质量分析

## 使用建议

### 最佳实践
1. **字段命名**: 尽量使用标准或接近标准的字段名
2. **数据格式**: 确保时间戳格式一致
3. **坐标精度**: 使用足够精度的坐标数据
4. **数据清洗**: 上传前进行基本的数据清洗

### 常见问题
1. **字段未识别**: 手动选择正确的映射关系
2. **时间格式错误**: 确保时间格式为标准格式
3. **坐标异常**: 检查坐标系统和数据来源
4. **速度异常**: 确认速度单位为km/h

## 未来扩展

### 计划功能
- **自定义映射规则**: 允许用户保存常用的映射配置
- **批量文件处理**: 支持多文件的统一字段映射
- **数据源适配器**: 支持更多数据源格式(JSON, XML等)
- **AI辅助映射**: 基于机器学习的智能字段识别

### 技术改进
- **性能优化**: 大文件的增量处理
- **内存管理**: 更高效的数据处理算法
- **错误恢复**: 更强的异常处理能力
- **国际化**: 支持更多语言的字段识别

---

**版本**: v1.0.0  
**更新日期**: 2024-06-27  
**开发者**: Augment Agent
