"""
车辆轨迹分析基础页面类
"""
import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import math
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')


class BasePage:
    """基础页面类，提供通用功能"""
    
    def __init__(self):
        self.title = "基础页面"
        self.description = "基础页面描述"
    
    def render(self):
        """渲染页面，子类需要重写此方法"""
        raise NotImplementedError("子类必须实现render方法")
    
    def get_data(self):
        """获取session state中的轨迹数据"""
        return st.session_state.get('trajectory_data', None)
    
    def set_data(self, data):
        """设置轨迹数据到session state"""
        st.session_state.trajectory_data = data
    
    def has_data(self):
        """检查是否有数据"""
        return 'trajectory_data' in st.session_state and st.session_state.trajectory_data is not None
    
    def validate_required_fields(self, df, required_fields):
        """验证必需字段是否存在"""
        missing_fields = [field for field in required_fields if field not in df.columns]
        if missing_fields:
            st.error(f"❌ 缺少必需字段: {', '.join(missing_fields)}")
            return False
        return True
    
    def show_data_info(self, df):
        """显示数据基本信息"""
        if df is not None and len(df) > 0:
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("数据量", f"{len(df):,} 条")
            
            with col2:
                if 'vehicle_id' in df.columns:
                    st.metric("车辆数量", df['vehicle_id'].nunique())
            
            with col3:
                if 'timestamp' in df.columns and len(df) > 1:
                    duration = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
                    st.metric("时间跨度（小时）", f"{duration:.2f}")
            
            with col4:
                if 'speed' in df.columns:
                    st.metric("平均速度（km/h）", f"{df['speed'].mean():.1f}")


class UtilityFunctions:
    """工具函数类"""
    
    @staticmethod
    def calculate_distance(lat1, lon1, lat2, lon2):
        """计算两点间距离（米）"""
        R = 6371000  # 地球半径（米）
        
        lat1_rad = math.radians(lat1)
        lat2_rad = math.radians(lat2)
        delta_lat = math.radians(lat2 - lat1)
        delta_lon = math.radians(lon2 - lon1)
        
        a = (math.sin(delta_lat/2) * math.sin(delta_lat/2) + 
             math.cos(lat1_rad) * math.cos(lat2_rad) * 
             math.sin(delta_lon/2) * math.sin(delta_lon/2))
        c = 2 * math.atan2(math.sqrt(a), math.sqrt(1-a))
        
        return R * c
    
    @staticmethod
    def calculate_speed(df):
        """计算速度（km/h）"""
        speeds = []
        for i in range(len(df)):
            if i == 0:
                speeds.append(0)
            else:
                # 计算距离
                dist = UtilityFunctions.calculate_distance(
                    df.iloc[i-1]['latitude'], df.iloc[i-1]['longitude'],
                    df.iloc[i]['latitude'], df.iloc[i]['longitude']
                )
                
                # 计算时间差（秒）
                time_diff = (df.iloc[i]['timestamp'] - df.iloc[i-1]['timestamp']).total_seconds()
                
                if time_diff > 0:
                    speed = (dist / time_diff) * 3.6  # 转换为km/h
                    speeds.append(speed)
                else:
                    speeds.append(0)
        
        return speeds
    
    @staticmethod
    def detect_speeding_events(df, speed_limit=60):
        """检测超速事件"""
        if 'speed' not in df.columns:
            return []
        
        speeding_events = []
        in_speeding = False
        start_idx = 0
        
        for i, speed in enumerate(df['speed']):
            if speed > speed_limit and not in_speeding:
                # 开始超速
                in_speeding = True
                start_idx = i
            elif speed <= speed_limit and in_speeding:
                # 结束超速
                in_speeding = False
                
                # 记录超速事件
                event_data = df.loc[start_idx:i-1]
                speeding_events.append({
                    'start_time': event_data.iloc[0]['timestamp'],
                    'end_time': event_data.iloc[-1]['timestamp'],
                    'duration': (event_data.iloc[-1]['timestamp'] - event_data.iloc[0]['timestamp']).total_seconds(),
                    'max_speed': event_data['speed'].max(),
                    'avg_speed': event_data['speed'].mean(),
                    'start_lat': event_data.iloc[0]['latitude'],
                    'start_lon': event_data.iloc[0]['longitude'],
                    'end_lat': event_data.iloc[-1]['latitude'],
                    'end_lon': event_data.iloc[-1]['longitude']
                })
        
        # 处理最后一个超速事件（如果数据在超速中结束）
        if in_speeding:
            event_data = df.loc[start_idx:]
            speeding_events.append({
                'start_time': event_data.iloc[0]['timestamp'],
                'end_time': event_data.iloc[-1]['timestamp'],
                'duration': (event_data.iloc[-1]['timestamp'] - event_data.iloc[0]['timestamp']).total_seconds(),
                'max_speed': event_data['speed'].max(),
                'avg_speed': event_data['speed'].mean(),
                'start_lat': event_data.iloc[0]['latitude'],
                'start_lon': event_data.iloc[0]['longitude'],
                'end_lat': event_data.iloc[-1]['latitude'],
                'end_lon': event_data.iloc[-1]['longitude']
            })
        
        return speeding_events
    
    @staticmethod
    def analyze_driving_behavior(df):
        """分析驾驶行为"""
        behavior_metrics = {}
        
        # 超速分析
        if 'speed' in df.columns:
            speed_limit = st.session_state.get('speed_limit', 60)
            speeding_events = UtilityFunctions.detect_speeding_events(df, speed_limit)
            behavior_metrics['speeding_events'] = len(speeding_events)
            behavior_metrics['total_speeding_time'] = sum([event['duration'] for event in speeding_events]) / 60  # 分钟
            
            # 急加速检测（速度变化率 > 2 m/s²）
            speed_changes = df['speed'].diff() / 3.6  # 转换为m/s
            time_diffs = df['timestamp'].diff().dt.total_seconds()
            accelerations = speed_changes / time_diffs
            
            behavior_metrics['harsh_accelerations'] = len(accelerations[accelerations > 2])
            behavior_metrics['harsh_decelerations'] = len(accelerations[accelerations < -2])
            
            # 速度变化频率
            behavior_metrics['speed_changes'] = len(df[abs(df['speed'].diff()) > 5])
        
        # 行驶距离
        if 'distance' in df.columns:
            behavior_metrics['total_distance'] = df['distance'].sum()
        
        # 行驶时间
        if len(df) > 1:
            behavior_metrics['total_time'] = (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds() / 3600
        
        return behavior_metrics
