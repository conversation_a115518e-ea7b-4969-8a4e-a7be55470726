import streamlit as st
import pandas as pd
import numpy as np
import plotly.express as px
import plotly.graph_objects as go
from io import StringIO

def render():
    """渲染数据分析页面"""
    
    st.markdown("""
    ### 📈 数据分析工具
    
    在这里您可以上传数据文件，进行各种数据分析和可视化操作。
    """)
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📁 数据导入", "🔍 数据探索", "📊 数据可视化", "🧮 统计分析"])
    
    with tab1:
        st.subheader("📁 数据导入")
        
        # 文件上传
        uploaded_file = st.file_uploader(
            "选择CSV文件",
            type=['csv'],
            help="支持CSV格式的数据文件"
        )
        
        if uploaded_file is not None:
            try:
                # 读取CSV文件
                df = pd.read_csv(uploaded_file)
                st.session_state.analysis_data = df
                
                st.success(f"✅ 文件上传成功！数据形状: {df.shape}")
                
                # 显示数据预览
                st.subheader("📋 数据预览")
                st.dataframe(df.head(10), use_container_width=True)
                
                # 数据基本信息
                col1, col2 = st.columns(2)
                
                with col1:
                    st.subheader("📊 数据信息")
                    st.write(f"- 行数: {df.shape[0]:,}")
                    st.write(f"- 列数: {df.shape[1]:,}")
                    st.write(f"- 内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
                
                with col2:
                    st.subheader("🔢 数据类型")
                    dtype_counts = df.dtypes.value_counts()
                    for dtype, count in dtype_counts.items():
                        st.write(f"- {dtype}: {count} 列")
                
            except Exception as e:
                st.error(f"❌ 文件读取失败: {e}")
        
        else:
            # 如果没有上传文件，使用示例数据
            st.info("💡 您可以上传CSV文件，或使用下面的示例数据进行体验")
            
            if st.button("🎲 加载示例数据", use_container_width=True):
                # 生成示例数据
                np.random.seed(42)
                n_samples = 1000
                
                df = pd.DataFrame({
                    '日期': pd.date_range('2024-01-01', periods=n_samples, freq='H'),
                    '销售额': np.random.normal(1000, 200, n_samples),
                    '访问量': np.random.poisson(50, n_samples),
                    '转化率': np.random.beta(2, 8, n_samples),
                    '地区': np.random.choice(['北京', '上海', '广州', '深圳'], n_samples),
                    '产品类型': np.random.choice(['A', 'B', 'C'], n_samples)
                })
                
                st.session_state.analysis_data = df
                st.success("✅ 示例数据加载成功！")
                st.rerun()
    
    with tab2:
        st.subheader("🔍 数据探索")
        
        if 'analysis_data' in st.session_state:
            df = st.session_state.analysis_data
            
            # 数据概览
            col1, col2 = st.columns(2)
            
            with col1:
                st.subheader("📈 描述性统计")
                numeric_cols = df.select_dtypes(include=[np.number]).columns
                if len(numeric_cols) > 0:
                    selected_col = st.selectbox("选择数值列", numeric_cols)
                    if selected_col:
                        st.dataframe(df[selected_col].describe(), use_container_width=True)
            
            with col2:
                st.subheader("🔍 缺失值分析")
                missing_data = df.isnull().sum()
                missing_data = missing_data[missing_data > 0]
                
                if len(missing_data) > 0:
                    st.dataframe(missing_data.to_frame('缺失值数量'), use_container_width=True)
                else:
                    st.success("✅ 没有发现缺失值")
            
            # 数据分布
            st.subheader("📊 数据分布")
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 0:
                selected_cols = st.multiselect(
                    "选择要分析的数值列",
                    numeric_cols,
                    default=numeric_cols[:3] if len(numeric_cols) >= 3 else numeric_cols
                )
                
                if selected_cols:
                    for col in selected_cols:
                        fig = px.histogram(df, x=col, title=f'{col} 分布图')
                        st.plotly_chart(fig, use_container_width=True)
        
        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或加载数据")
    
    with tab3:
        st.subheader("📊 数据可视化")
        
        if 'analysis_data' in st.session_state:
            df = st.session_state.analysis_data
            
            # 图表类型选择
            chart_type = st.selectbox(
                "选择图表类型",
                ["散点图", "折线图", "柱状图", "箱线图", "热力图"]
            )
            
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            categorical_cols = df.select_dtypes(include=['object']).columns
            
            if chart_type == "散点图":
                col1, col2 = st.columns(2)
                with col1:
                    x_col = st.selectbox("X轴", numeric_cols)
                with col2:
                    y_col = st.selectbox("Y轴", numeric_cols)
                
                if x_col and y_col:
                    color_col = st.selectbox("颜色分组（可选）", [None] + list(categorical_cols))
                    
                    fig = px.scatter(df, x=x_col, y=y_col, color=color_col,
                                   title=f'{x_col} vs {y_col}')
                    st.plotly_chart(fig, use_container_width=True)
            
            elif chart_type == "折线图":
                if len(numeric_cols) > 0:
                    y_cols = st.multiselect("选择Y轴变量", numeric_cols)
                    
                    if y_cols:
                        fig = go.Figure()
                        for col in y_cols:
                            fig.add_trace(go.Scatter(
                                x=df.index,
                                y=df[col],
                                mode='lines',
                                name=col
                            ))
                        
                        fig.update_layout(title="时间序列图")
                        st.plotly_chart(fig, use_container_width=True)
            
            elif chart_type == "柱状图":
                if len(categorical_cols) > 0:
                    cat_col = st.selectbox("分类变量", categorical_cols)
                    
                    if cat_col:
                        value_counts = df[cat_col].value_counts()
                        fig = px.bar(x=value_counts.index, y=value_counts.values,
                                   title=f'{cat_col} 分布')
                        st.plotly_chart(fig, use_container_width=True)
            
            elif chart_type == "箱线图":
                if len(numeric_cols) > 0:
                    num_col = st.selectbox("数值变量", numeric_cols)
                    cat_col = st.selectbox("分组变量（可选）", [None] + list(categorical_cols))
                    
                    if num_col:
                        fig = px.box(df, y=num_col, x=cat_col,
                                   title=f'{num_col} 箱线图')
                        st.plotly_chart(fig, use_container_width=True)
            
            elif chart_type == "热力图":
                if len(numeric_cols) > 1:
                    corr_matrix = df[numeric_cols].corr()
                    fig = px.imshow(corr_matrix, text_auto=True,
                                  title="相关性热力图")
                    st.plotly_chart(fig, use_container_width=True)
        
        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或加载数据")
    
    with tab4:
        st.subheader("🧮 统计分析")
        
        if 'analysis_data' in st.session_state:
            df = st.session_state.analysis_data
            
            # 相关性分析
            st.subheader("📊 相关性分析")
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            
            if len(numeric_cols) > 1:
                corr_matrix = df[numeric_cols].corr()
                st.dataframe(corr_matrix, use_container_width=True)
                
                # 找出最强相关性
                corr_pairs = []
                for i in range(len(corr_matrix.columns)):
                    for j in range(i+1, len(corr_matrix.columns)):
                        corr_pairs.append({
                            '变量1': corr_matrix.columns[i],
                            '变量2': corr_matrix.columns[j],
                            '相关系数': corr_matrix.iloc[i, j]
                        })
                
                corr_df = pd.DataFrame(corr_pairs)
                corr_df = corr_df.reindex(corr_df['相关系数'].abs().sort_values(ascending=False).index)
                
                st.subheader("🔝 最强相关性")
                st.dataframe(corr_df.head(10), use_container_width=True)
            
            # 分组统计
            st.subheader("📊 分组统计")
            categorical_cols = df.select_dtypes(include=['object']).columns
            
            if len(categorical_cols) > 0 and len(numeric_cols) > 0:
                group_col = st.selectbox("分组变量", categorical_cols)
                agg_col = st.selectbox("聚合变量", numeric_cols)
                
                if group_col and agg_col:
                    grouped = df.groupby(group_col)[agg_col].agg(['count', 'mean', 'std', 'min', 'max'])
                    st.dataframe(grouped, use_container_width=True)
        
        else:
            st.warning("⚠️ 请先在'数据导入'标签页中上传或加载数据")
