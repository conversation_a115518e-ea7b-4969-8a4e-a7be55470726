# Streamlit应用程序模板

这是一个使用Streamlit构建的模块化应用程序模板，具有左侧工具栏和页面切换功能。

## 🎯 功能特性

- **🎛️ 左侧工具栏**: 分组显示的导航按钮，支持页面切换
- **📱 响应式设计**: 自适应不同屏幕尺寸
- **🔄 模块化架构**: 每个页面作为独立模块，易于维护和扩展
- **🎨 美观界面**: 现代化的UI设计和交互效果
- **📊 丰富组件**: 包含图表、表格、表单等多种Streamlit组件

## 📁 项目结构

```
.
├── streamlit_app.py          # 主应用程序文件
├── run_streamlit_app.py      # 启动脚本
├── pages/                    # 页面模块目录
│   ├── __init__.py
│   ├── dashboard.py          # 仪表板页面
│   ├── data_analysis.py      # 数据分析页面
│   ├── charts.py             # 图表展示页面
│   ├── reports.py            # 报告中心页面
│   ├── tools.py              # 实用工具页面
│   └── settings.py           # 系统设置页面
├── utils/                    # 工具函数目录
│   └── __init__.py
├── components/               # 自定义组件目录
│   └── __init__.py
└── STREAMLIT_APP_README.md   # 说明文档
```

## 🚀 快速开始

### 1. 环境要求

- Python 3.11+
- 已安装的依赖包（见下方安装说明）

### 2. 安装依赖

如果使用uv（推荐）:
```bash
uv add streamlit plotly pandas numpy
```

或使用pip:
```bash
pip install streamlit plotly pandas numpy
```

### 3. 运行应用程序

**方法一：使用启动脚本（推荐）**
```bash
python run_streamlit_app.py
```

**方法二：直接运行Streamlit**
```bash
streamlit run streamlit_app.py
```

### 4. 访问应用程序

打开浏览器访问: http://localhost:8501

## 📊 页面功能

### 🏠 仪表板 (Dashboard)
- 关键指标展示
- 用户增长趋势图
- 今日任务列表
- 最近活动记录
- 地区分布饼图
- 系统状态监控

### 📈 数据分析 (Data Analysis)
- **数据导入**: 支持CSV文件上传，提供示例数据
- **数据探索**: 描述性统计、缺失值分析、数据分布
- **数据可视化**: 散点图、折线图、柱状图、箱线图、热力图
- **统计分析**: 相关性分析、分组统计

### 📉 图表展示 (Charts)
- **基础图表**: 折线图、柱状图、散点图、饼图
- **高级图表**: 多指标仪表板、3D散点图、子图表
- **实时图表**: 实时监控、仪表盘
- **自定义图表**: 可配置的图表生成器

### 📋 报告中心 (Reports)
- **生成报告**: 多种报告类型、日期范围选择
- **历史报告**: 报告列表、搜索筛选
- **报告设置**: 邮件设置、存储配置、模板管理

### 🔧 实用工具 (Tools)
- **数据转换**: CSV ↔ JSON转换
- **文本处理**: 文本统计、格式转换
- **时间工具**: 时间戳转换、日期计算
- **编码解码**: Base64编码/解码
- **随机生成**: 随机数、UUID、密码生成
- **颜色工具**: 颜色转换、调色板生成

### ⚙️ 系统设置 (Settings)
- **用户设置**: 个人信息、头像、密码修改
- **界面设置**: 主题、颜色、字体、布局
- **通知设置**: 邮件、推送、声音通知
- **系统配置**: 数据备份、网络设置、系统信息

## 🔧 自定义和扩展

### 添加新页面

1. **创建页面模块**
在`pages/`目录下创建新的Python文件，例如`new_page.py`:

```python
import streamlit as st

def render():
    """渲染新页面"""
    st.markdown("### 🆕 新页面")
    st.write("这是一个新页面的内容")
    
    # 添加页面功能...
```

2. **注册页面**
在`streamlit_app.py`的`AppManager`类中添加页面配置:

```python
self.pages = {
    # 现有页面...
    "new_page": {
        "name": "🆕 新页面",
        "module": "pages.new_page",
        "group": "主要功能"  # 或 "管理功能"
    }
}
```

### 修改样式

在`streamlit_app.py`中的CSS样式部分添加自定义样式:

```python
st.markdown("""
<style>
    /* 添加自定义CSS */
    .custom-class {
        /* 样式规则 */
    }
</style>
""", unsafe_allow_html=True)
```

### 添加工具函数

在`utils/`目录下创建工具模块:

```python
# utils/helpers.py
def custom_function():
    """自定义工具函数"""
    pass
```

在页面中使用:

```python
from utils.helpers import custom_function
```

## 🎨 界面特性

- **响应式布局**: 自动适应不同屏幕尺寸
- **现代化设计**: 使用渐变色、圆角、阴影等现代UI元素
- **交互效果**: 按钮悬停效果、状态指示
- **分组导航**: 工具栏按功能分组显示
- **状态管理**: 使用session_state保持页面状态

## 📊 数据处理

- **文件上传**: 支持CSV、JSON等格式
- **数据可视化**: 集成Plotly实现交互式图表
- **统计分析**: 内置常用统计分析功能
- **数据导出**: 支持多种格式的数据导出

## 🔔 注意事项

1. **端口配置**: 默认使用8501端口，如被占用会自动选择其他端口
2. **浏览器兼容**: 建议使用Chrome、Firefox、Safari等现代浏览器
3. **性能优化**: 大数据量时建议使用数据采样或分页显示
4. **内存管理**: 注意清理不需要的session_state数据

## 🚀 部署建议

### 本地开发
```bash
streamlit run streamlit_app.py --server.port=8501
```

### 生产部署
```bash
streamlit run streamlit_app.py --server.port=8501 --server.headless=true
```

### Docker部署
```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY . .

RUN pip install streamlit plotly pandas numpy

EXPOSE 8501

CMD ["streamlit", "run", "streamlit_app.py", "--server.port=8501", "--server.headless=true"]
```

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 📄 许可证

MIT License

## 🆘 故障排除

### 常见问题

1. **模块导入错误**
   - 确保所有依赖已正确安装
   - 检查Python路径配置

2. **端口占用**
   - 修改启动脚本中的端口号
   - 或使用`--server.port`参数指定端口

3. **页面加载失败**
   - 检查页面模块的`render()`函数是否正确定义
   - 查看控制台错误信息

4. **样式显示异常**
   - 清除浏览器缓存
   - 检查CSS语法是否正确

### 获取帮助

- 查看Streamlit官方文档: https://docs.streamlit.io/
- 提交Issue到项目仓库
- 联系开发团队

---

**享受使用Streamlit构建应用程序的乐趣！** 🎉
